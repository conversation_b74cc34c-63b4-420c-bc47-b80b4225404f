-- Enterprise Technical Support Platform Database Schema
-- Database: enterprise_support_platform
-- Version: 2.0 - Enterprise Grade
-- Created: 2025-07-06

-- =============================================================================
-- CORE SYSTEM TABLES
-- =============================================================================

-- Organizations and Departments
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    manager_email VARCHAR(255),
    cost_center VARCHAR(100),
    location VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, name)
);

-- Enhanced Users Management
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    department_id UUID REFERENCES departments(id) ON DELETE SET NULL,

    -- Identity
    user_id VARCHAR(255) UNIQUE NOT NULL, -- Azure AD/External ID
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    employee_id VARCHAR(100),

    -- Role and Access
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'agent', 'supervisor', 'admin', 'system')),
    permissions JSONB DEFAULT '[]',
    agent_groups TEXT[] DEFAULT ARRAY[]::TEXT[],
    skill_tags TEXT[] DEFAULT ARRAY[]::TEXT[],

    -- Profile Information
    phone VARCHAR(50),
    location VARCHAR(255),
    timezone VARCHAR(100) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    title VARCHAR(255),
    manager_email VARCHAR(255),

    -- System Fields
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    password_last_changed TIMESTAMP WITH TIME ZONE,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,

    -- Preferences
    notification_preferences JSONB DEFAULT '{"email": true, "sms": false, "push": true}',
    ui_preferences JSONB DEFAULT '{}',

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Service Configuration
CREATE TABLE service_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES service_categories(id),
    icon VARCHAR(100),
    color VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    auto_assignment_rules JSONB DEFAULT '{}',
    sla_rules JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, code)
);

CREATE TABLE service_subcategories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id UUID REFERENCES service_categories(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) NOT NULL,
    description TEXT,
    estimated_resolution_time INTERVAL,
    auto_resolution_enabled BOOLEAN DEFAULT FALSE,
    escalation_threshold INTERVAL,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category_id, code)
);

-- =============================================================================
-- TICKET MANAGEMENT SYSTEM
-- =============================================================================

-- Priority and Status Definitions
CREATE TABLE ticket_priorities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(20) NOT NULL,
    level INTEGER NOT NULL, -- 1=Low, 2=Medium, 3=High, 4=Critical, 5=Emergency
    color VARCHAR(20),
    sla_response_time INTERVAL,
    sla_resolution_time INTERVAL,
    escalation_time INTERVAL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, code),
    UNIQUE(organization_id, level)
);

CREATE TABLE ticket_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(20) NOT NULL,
    category VARCHAR(20) NOT NULL CHECK (category IN ('open', 'in_progress', 'pending', 'resolved', 'closed', 'cancelled')),
    is_final BOOLEAN DEFAULT FALSE,
    is_billable BOOLEAN DEFAULT TRUE,
    color VARCHAR(20),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, code)
);

-- Main Tickets Table - Enterprise Grade
CREATE TABLE tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Ticket Identification
    ticket_number VARCHAR(50) UNIQUE NOT NULL, -- AUTO-GENERATED: ORG-YYYY-NNNNNN
    external_ticket_id VARCHAR(100), -- For integration with external systems

    -- Basic Information
    subject VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    category_id UUID REFERENCES service_categories(id),
    subcategory_id UUID REFERENCES service_subcategories(id),

    -- Priority and Status
    priority_id UUID REFERENCES ticket_priorities(id),
    status_id UUID REFERENCES ticket_statuses(id),
    urgency VARCHAR(20) DEFAULT 'medium' CHECK (urgency IN ('low', 'medium', 'high', 'critical')),
    impact VARCHAR(20) DEFAULT 'medium' CHECK (impact IN ('low', 'medium', 'high', 'critical')),

    -- Requester Information
    requester_id UUID REFERENCES users(id),
    requester_email VARCHAR(255) NOT NULL,
    requester_name VARCHAR(255) NOT NULL,
    requester_phone VARCHAR(50),
    requester_location VARCHAR(255),
    on_behalf_of_id UUID REFERENCES users(id), -- For proxy requests

    -- Assignment
    assigned_to_id UUID REFERENCES users(id),
    assigned_group VARCHAR(100),
    assigned_at TIMESTAMP WITH TIME ZONE,
    assigned_by_id UUID REFERENCES users(id),

    -- Resolution
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by_id UUID REFERENCES users(id),
    resolution_notes TEXT,
    resolution_category VARCHAR(100),
    root_cause TEXT,

    -- SLA Tracking
    sla_response_due TIMESTAMP WITH TIME ZONE,
    sla_resolution_due TIMESTAMP WITH TIME ZONE,
    first_response_at TIMESTAMP WITH TIME ZONE,
    first_response_by_id UUID REFERENCES users(id),
    sla_breach_response BOOLEAN DEFAULT FALSE,
    sla_breach_resolution BOOLEAN DEFAULT FALSE,

    -- AI and Automation
    ai_generated BOOLEAN DEFAULT FALSE,
    ai_confidence_score NUMERIC(5,4), -- 0.0000 to 1.0000
    ai_suggested_category VARCHAR(255),
    ai_suggested_priority VARCHAR(50),
    auto_resolved BOOLEAN DEFAULT FALSE,
    auto_resolution_reason TEXT,
    escalation_reason TEXT,
    conversation_summary TEXT,

    -- Technical Details
    affected_systems TEXT[] DEFAULT ARRAY[]::TEXT[],
    error_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
    symptoms TEXT[] DEFAULT ARRAY[]::TEXT[],
    environment VARCHAR(50) DEFAULT 'production',

    -- Business Impact
    business_impact TEXT,
    affected_users_count INTEGER DEFAULT 1,
    estimated_cost NUMERIC(12,2),
    cost_currency VARCHAR(10) DEFAULT 'USD',

    -- Source and Channel
    source VARCHAR(50) DEFAULT 'web' CHECK (source IN ('web', 'email', 'phone', 'chat', 'api', 'mobile', 'walk_in')),
    channel VARCHAR(50),
    session_id UUID, -- Links to chat/conversation session

    -- Customer Satisfaction
    satisfaction_rating INTEGER CHECK (satisfaction_rating BETWEEN 1 AND 5),
    satisfaction_feedback TEXT,
    satisfaction_date TIMESTAMP WITH TIME ZONE,

    -- Metadata and Tracking
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    custom_fields JSONB DEFAULT '{}',
    attachments JSONB DEFAULT '[]',
    integration_data JSONB DEFAULT '{}', -- For external system data

    -- Audit Fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id UUID REFERENCES users(id),
    updated_by_id UUID REFERENCES users(id),
    version INTEGER DEFAULT 1,

    -- Closure
    closed_at TIMESTAMP WITH TIME ZONE,
    closed_by_id UUID REFERENCES users(id),
    closure_code VARCHAR(100),

    -- Parent/Child Relationships
    parent_ticket_id UUID REFERENCES tickets(id),
    is_parent BOOLEAN DEFAULT FALSE,

    -- Time Tracking
    total_time_spent INTERVAL DEFAULT '0 minutes',
    billable_time INTERVAL DEFAULT '0 minutes'
);
-- Ticket Activities and Communication
CREATE TABLE ticket_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID REFERENCES tickets(id) ON DELETE CASCADE,

    -- Activity Details
    activity_type VARCHAR(50) NOT NULL CHECK (activity_type IN (
        'comment', 'status_change', 'assignment', 'escalation', 'resolution',
        'reopening', 'priority_change', 'category_change', 'attachment',
        'time_entry', 'sla_breach', 'auto_action', 'ai_interaction', 'email_sent'
    )),

    -- Actor Information
    actor_id UUID REFERENCES users(id),
    actor_name VARCHAR(255) NOT NULL,
    actor_type VARCHAR(20) NOT NULL CHECK (actor_type IN ('user', 'agent', 'system', 'ai', 'external')),
    actor_email VARCHAR(255),

    -- Content
    title VARCHAR(255),
    content TEXT,
    content_type VARCHAR(50) DEFAULT 'text', -- text, html, markdown

    -- Visibility and Access
    visibility VARCHAR(20) DEFAULT 'internal' CHECK (visibility IN ('public', 'internal', 'agent_only', 'private')),
    is_customer_visible BOOLEAN DEFAULT FALSE,

    -- Change Tracking
    field_changes JSONB DEFAULT '{}', -- {"field": {"old": "value", "new": "value"}}
    previous_values JSONB DEFAULT '{}',

    -- Time Tracking
    time_spent INTERVAL,
    billable_time INTERVAL,
    work_type VARCHAR(100),

    -- Metadata
    source VARCHAR(50), -- email, web, api, mobile, system
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Email Integration
    email_message_id VARCHAR(255),
    email_thread_id VARCHAR(255),

    -- AI Specific
    ai_confidence NUMERIC(5,4),
    ai_model_used VARCHAR(100),
    ai_processing_time INTERVAL
);

-- Ticket Attachments
CREATE TABLE ticket_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID REFERENCES tickets(id) ON DELETE CASCADE,
    activity_id UUID REFERENCES ticket_activities(id) ON DELETE SET NULL,

    -- File Information
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(255),
    file_hash VARCHAR(128), -- SHA-256 hash for deduplication

    -- Upload Information
    uploaded_by_id UUID REFERENCES users(id),
    uploaded_by_name VARCHAR(255) NOT NULL,
    upload_source VARCHAR(50), -- web, email, api, mobile

    -- Security and Access
    is_public BOOLEAN DEFAULT FALSE,
    access_level VARCHAR(20) DEFAULT 'internal',
    virus_scan_status VARCHAR(20) DEFAULT 'pending',
    virus_scan_result TEXT,

    -- Metadata
    description TEXT,
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    metadata JSONB DEFAULT '{}',

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by_id UUID REFERENCES users(id)
);

-- =============================================================================
-- AUTOMATION AND WORKFLOW MANAGEMENT
-- =============================================================================

-- Automation Rules Engine
CREATE TABLE automation_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Rule Definition
    name VARCHAR(255) NOT NULL,
    description TEXT,
    rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN (
        'auto_assign', 'auto_categorize', 'auto_escalate', 'auto_resolve',
        'auto_priority', 'auto_notify', 'auto_close', 'sla_reminder'
    )),

    -- Conditions (JSON-based rule engine)
    conditions JSONB NOT NULL, -- Complex condition logic
    actions JSONB NOT NULL, -- Actions to execute

    -- Execution Control
    is_active BOOLEAN DEFAULT TRUE,
    execution_order INTEGER DEFAULT 0,
    max_executions_per_ticket INTEGER DEFAULT 1,
    cooldown_period INTERVAL DEFAULT '0 minutes',

    -- Scope and Filters
    applies_to_categories UUID[] DEFAULT ARRAY[]::UUID[],
    applies_to_priorities UUID[] DEFAULT ARRAY[]::UUID[],
    applies_to_sources TEXT[] DEFAULT ARRAY[]::TEXT[],
    time_restrictions JSONB DEFAULT '{}', -- Business hours, days of week

    -- Performance Tracking
    execution_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    last_executed TIMESTAMP WITH TIME ZONE,
    average_execution_time INTERVAL,

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id UUID REFERENCES users(id),
    updated_by_id UUID REFERENCES users(id),

    -- Version Control
    version INTEGER DEFAULT 1,
    is_template BOOLEAN DEFAULT FALSE,
    parent_rule_id UUID REFERENCES automation_rules(id)
);
-- Rule Execution Log
CREATE TABLE automation_rule_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_id UUID REFERENCES automation_rules(id) ON DELETE CASCADE,
    ticket_id UUID REFERENCES tickets(id) ON DELETE CASCADE,

    -- Execution Details
    execution_status VARCHAR(20) NOT NULL CHECK (execution_status IN ('success', 'failure', 'skipped', 'error')),
    execution_time INTERVAL,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Results
    actions_taken JSONB DEFAULT '{}',
    error_message TEXT,
    conditions_met JSONB DEFAULT '{}',

    -- Context
    ticket_state_before JSONB,
    ticket_state_after JSONB,
    triggered_by VARCHAR(50), -- manual, automatic, scheduled
    triggered_by_user_id UUID REFERENCES users(id)
);

-- =============================================================================
-- KNOWLEDGE MANAGEMENT SYSTEM
-- =============================================================================

-- Knowledge Base Articles
CREATE TABLE knowledge_articles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Article Content
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    content_type VARCHAR(20) DEFAULT 'markdown', -- markdown, html, text
    summary TEXT,
    keywords TEXT[] DEFAULT ARRAY[]::TEXT[],

    -- Categorization
    category_id UUID REFERENCES service_categories(id),
    subcategory_id UUID REFERENCES service_subcategories(id),
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],

    -- Status and Workflow
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived', 'deprecated')),
    approval_status VARCHAR(20) DEFAULT 'pending',

    -- Authoring
    author_id UUID REFERENCES users(id),
    reviewer_id UUID REFERENCES users(id),
    approved_by_id UUID REFERENCES users(id),

    -- Versioning
    version INTEGER DEFAULT 1,
    parent_article_id UUID REFERENCES knowledge_articles(id),
    is_latest_version BOOLEAN DEFAULT TRUE,

    -- Usage Analytics
    view_count INTEGER DEFAULT 0,
    helpful_votes INTEGER DEFAULT 0,
    unhelpful_votes INTEGER DEFAULT 0,
    last_viewed TIMESTAMP WITH TIME ZONE,

    -- SEO and Search
    meta_description TEXT,
    search_keywords TEXT[] DEFAULT ARRAY[]::TEXT[],
    difficulty_level VARCHAR(20) DEFAULT 'beginner', -- beginner, intermediate, advanced
    estimated_read_time INTERVAL,

    -- Lifecycle
    published_at TIMESTAMP WITH TIME ZONE,
    review_due_date TIMESTAMP WITH TIME ZONE,
    last_reviewed TIMESTAMP WITH TIME ZONE,

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id UUID REFERENCES users(id),
    updated_by_id UUID REFERENCES users(id)
);

-- Knowledge Article Usage Tracking
CREATE TABLE knowledge_article_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    article_id UUID REFERENCES knowledge_articles(id) ON DELETE CASCADE,
    ticket_id UUID REFERENCES tickets(id) ON DELETE SET NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,

    -- Usage Context
    usage_type VARCHAR(50) NOT NULL, -- view, helpful_vote, unhelpful_vote, shared, applied
    context VARCHAR(100), -- search, recommendation, manual_browse, ticket_suggestion

    -- Effectiveness
    was_helpful BOOLEAN,
    feedback_text TEXT,
    resolved_issue BOOLEAN,

    -- Session Information
    session_id UUID,
    ip_address INET,
    user_agent TEXT,

    -- Timing
    time_spent INTERVAL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =============================================================================
-- SLA AND SERVICE LEVEL MANAGEMENT
-- =============================================================================

-- SLA Policies
CREATE TABLE sla_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Policy Definition
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,

    -- Scope
    applies_to_categories UUID[] DEFAULT ARRAY[]::UUID[],
    applies_to_priorities UUID[] DEFAULT ARRAY[]::UUID[],
    applies_to_departments UUID[] DEFAULT ARRAY[]::UUID[],
    applies_to_user_roles TEXT[] DEFAULT ARRAY[]::TEXT[],

    -- Time Targets
    response_time_target INTERVAL NOT NULL,
    resolution_time_target INTERVAL NOT NULL,
    escalation_time INTERVAL,

    -- Business Hours
    business_hours_only BOOLEAN DEFAULT TRUE,
    timezone VARCHAR(100) DEFAULT 'UTC',

    -- Escalation Rules
    escalation_enabled BOOLEAN DEFAULT TRUE,
    escalation_levels JSONB DEFAULT '[]',

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id UUID REFERENCES users(id)
);

-- Business Hours Configuration
CREATE TABLE business_hours (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Schedule Definition
    name VARCHAR(255) NOT NULL,
    timezone VARCHAR(100) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,

    -- Weekly Schedule (JSON format for flexibility)
    schedule JSONB NOT NULL, -- {"monday": {"start": "09:00", "end": "17:00", "enabled": true}, ...}

    -- Holidays
    exclude_holidays BOOLEAN DEFAULT TRUE,
    holiday_calendar_id UUID,

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Holiday Calendar
CREATE TABLE holidays (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Holiday Details
    name VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern VARCHAR(100), -- yearly, monthly, etc.

    -- Scope
    applies_to_locations TEXT[] DEFAULT ARRAY[]::TEXT[],
    applies_to_departments UUID[] DEFAULT ARRAY[]::UUID[],

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, date, name)
);
-- =============================================================================
-- CHAT AND CONVERSATION MANAGEMENT
-- =============================================================================

-- Chat Sessions
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Session Information
    session_token VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id),
    user_email VARCHAR(255),
    user_name VARCHAR(255),

    -- Session State
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'escalated', 'resolved', 'abandoned', 'transferred')),
    escalated_to_ticket_id UUID REFERENCES tickets(id),

    -- Context and Intent
    initial_intent VARCHAR(255),
    detected_language VARCHAR(10) DEFAULT 'en',
    user_sentiment VARCHAR(20), -- positive, neutral, negative, frustrated
    complexity_score NUMERIC(3,2), -- 0.00 to 1.00

    -- Session Metadata
    source VARCHAR(50) DEFAULT 'web', -- web, mobile, api, widget
    user_agent TEXT,
    ip_address INET,
    referrer_url TEXT,

    -- Timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP WITH TIME ZONE,
    total_duration INTERVAL,

    -- AI Performance
    ai_satisfaction_score NUMERIC(3,2),
    resolution_achieved BOOLEAN DEFAULT FALSE,
    escalation_reason TEXT,

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Chat Messages
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,

    -- Message Content
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system', 'agent')),
    content TEXT NOT NULL,
    content_type VARCHAR(20) DEFAULT 'text', -- text, html, markdown, json

    -- AI Processing
    intent_detected VARCHAR(255),
    confidence_score NUMERIC(5,4),
    processing_time INTERVAL,
    model_used VARCHAR(100),

    -- Knowledge Base Integration
    knowledge_sources JSONB DEFAULT '[]',
    citations TEXT[] DEFAULT ARRAY[]::TEXT[],

    -- Message Metadata
    message_index INTEGER NOT NULL, -- Order within session
    is_sensitive BOOLEAN DEFAULT FALSE,
    contains_pii BOOLEAN DEFAULT FALSE,

    -- Feedback
    user_feedback VARCHAR(20), -- helpful, not_helpful, irrelevant
    feedback_reason TEXT,

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(session_id, message_index)
);

-- =============================================================================
-- ANALYTICS AND REPORTING
-- =============================================================================

-- Performance Metrics (Daily Aggregates)
CREATE TABLE daily_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Date and Scope
    metric_date DATE NOT NULL,
    department_id UUID REFERENCES departments(id),
    category_id UUID REFERENCES service_categories(id),
    agent_id UUID REFERENCES users(id),

    -- Ticket Metrics
    tickets_created INTEGER DEFAULT 0,
    tickets_resolved INTEGER DEFAULT 0,
    tickets_closed INTEGER DEFAULT 0,
    tickets_escalated INTEGER DEFAULT 0,
    tickets_reopened INTEGER DEFAULT 0,

    -- Time Metrics
    avg_first_response_time INTERVAL,
    avg_resolution_time INTERVAL,
    avg_customer_wait_time INTERVAL,

    -- SLA Metrics
    sla_breaches_response INTEGER DEFAULT 0,
    sla_breaches_resolution INTEGER DEFAULT 0,
    sla_compliance_rate NUMERIC(5,2),

    -- AI Metrics
    ai_interactions INTEGER DEFAULT 0,
    ai_resolutions INTEGER DEFAULT 0,
    ai_escalations INTEGER DEFAULT 0,
    avg_ai_confidence NUMERIC(5,4),

    -- Customer Satisfaction
    satisfaction_responses INTEGER DEFAULT 0,
    avg_satisfaction_rating NUMERIC(3,2),

    -- Knowledge Base
    kb_article_views INTEGER DEFAULT 0,
    kb_helpful_votes INTEGER DEFAULT 0,
    kb_unhelpful_votes INTEGER DEFAULT 0,

    -- Automation
    automation_executions INTEGER DEFAULT 0,
    automation_successes INTEGER DEFAULT 0,

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(organization_id, metric_date, department_id, category_id, agent_id)
);

-- System Configuration
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,

    -- Setting Details
    setting_key VARCHAR(255) NOT NULL,
    setting_value JSONB NOT NULL,
    setting_type VARCHAR(50) NOT NULL, -- string, number, boolean, json, array

    -- Metadata
    description TEXT,
    is_sensitive BOOLEAN DEFAULT FALSE,
    is_user_configurable BOOLEAN DEFAULT TRUE,

    -- Validation
    validation_rules JSONB DEFAULT '{}',
    default_value JSONB,

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by_id UUID REFERENCES users(id),

    UNIQUE(organization_id, setting_key)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Core Entity Indexes
CREATE INDEX idx_users_organization_id ON users(organization_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);

-- Ticket Indexes
CREATE INDEX idx_tickets_organization_id ON tickets(organization_id);
CREATE INDEX idx_tickets_ticket_number ON tickets(ticket_number);
CREATE INDEX idx_tickets_status_id ON tickets(status_id);
CREATE INDEX idx_tickets_priority_id ON tickets(priority_id);
CREATE INDEX idx_tickets_category_id ON tickets(category_id);
CREATE INDEX idx_tickets_requester_id ON tickets(requester_id);
CREATE INDEX idx_tickets_assigned_to_id ON tickets(assigned_to_id);
CREATE INDEX idx_tickets_created_at ON tickets(created_at);
CREATE INDEX idx_tickets_updated_at ON tickets(updated_at);
CREATE INDEX idx_tickets_resolved_at ON tickets(resolved_at);
CREATE INDEX idx_tickets_sla_response_due ON tickets(sla_response_due);
CREATE INDEX idx_tickets_sla_resolution_due ON tickets(sla_resolution_due);
CREATE INDEX idx_tickets_ai_generated ON tickets(ai_generated);
CREATE INDEX idx_tickets_source ON tickets(source);

-- Activity Indexes
CREATE INDEX idx_ticket_activities_ticket_id ON ticket_activities(ticket_id);
CREATE INDEX idx_ticket_activities_created_at ON ticket_activities(created_at);
CREATE INDEX idx_ticket_activities_activity_type ON ticket_activities(activity_type);
CREATE INDEX idx_ticket_activities_actor_id ON ticket_activities(actor_id);

-- Chat Indexes
CREATE INDEX idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX idx_chat_sessions_started_at ON chat_sessions(started_at);
CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);

-- Knowledge Base Indexes
CREATE INDEX idx_knowledge_articles_organization_id ON knowledge_articles(organization_id);
CREATE INDEX idx_knowledge_articles_status ON knowledge_articles(status);
CREATE INDEX idx_knowledge_articles_category_id ON knowledge_articles(category_id);
CREATE INDEX idx_knowledge_articles_tags ON knowledge_articles USING GIN(tags);

-- Analytics Indexes
CREATE INDEX idx_daily_metrics_organization_id ON daily_metrics(organization_id);
CREATE INDEX idx_daily_metrics_metric_date ON daily_metrics(metric_date);
CREATE INDEX idx_daily_metrics_department_id ON daily_metrics(department_id);

-- Full-text Search Indexes
CREATE INDEX idx_tickets_search ON tickets USING GIN(to_tsvector('english', subject || ' ' || description));
CREATE INDEX idx_knowledge_articles_search ON knowledge_articles USING GIN(to_tsvector('english', title || ' ' || content));

-- =============================================================================
-- TRIGGERS AND FUNCTIONS
-- =============================================================================

-- Update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers to all tables with updated_at
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON departments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tickets_updated_at BEFORE UPDATE ON tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ticket_activities_updated_at BEFORE UPDATE ON ticket_activities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_knowledge_articles_updated_at BEFORE UPDATE ON knowledge_articles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chat_sessions_updated_at BEFORE UPDATE ON chat_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Ticket number generation function
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TRIGGER AS $$
DECLARE
    org_code VARCHAR(10);
    year_part VARCHAR(4);
    sequence_num INTEGER;
    new_ticket_number VARCHAR(50);
BEGIN
    -- Get organization code (first 3 chars of domain, uppercase)
    SELECT UPPER(LEFT(domain, 3)) INTO org_code
    FROM organizations
    WHERE id = NEW.organization_id;

    -- Get current year
    year_part := TO_CHAR(CURRENT_DATE, 'YYYY');

    -- Get next sequence number for this org/year
    SELECT COALESCE(MAX(CAST(RIGHT(ticket_number, 6) AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM tickets
    WHERE organization_id = NEW.organization_id
    AND ticket_number LIKE org_code || '-' || year_part || '-%';

    -- Generate ticket number: ORG-YYYY-NNNNNN
    new_ticket_number := org_code || '-' || year_part || '-' || LPAD(sequence_num::TEXT, 6, '0');

    NEW.ticket_number := new_ticket_number;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER generate_ticket_number_trigger
    BEFORE INSERT ON tickets
    FOR EACH ROW
    WHEN (NEW.ticket_number IS NULL)
    EXECUTE FUNCTION generate_ticket_number();

-- SLA calculation function
CREATE OR REPLACE FUNCTION calculate_sla_due_dates()
RETURNS TRIGGER AS $$
DECLARE
    sla_policy RECORD;
    business_hours_config RECORD;
BEGIN
    -- Get applicable SLA policy
    SELECT sp.* INTO sla_policy
    FROM sla_policies sp
    WHERE sp.organization_id = NEW.organization_id
    AND sp.is_active = TRUE
    AND (sp.applies_to_categories = '{}' OR NEW.category_id = ANY(sp.applies_to_categories))
    AND (sp.applies_to_priorities = '{}' OR NEW.priority_id = ANY(sp.applies_to_priorities))
    ORDER BY
        CASE WHEN NEW.category_id = ANY(sp.applies_to_categories) THEN 1 ELSE 2 END,
        CASE WHEN NEW.priority_id = ANY(sp.applies_to_priorities) THEN 1 ELSE 2 END,
        sp.created_at
    LIMIT 1;

    IF FOUND THEN
        -- Calculate response due date
        IF sla_policy.business_hours_only THEN
            -- TODO: Implement business hours calculation
            NEW.sla_response_due := NEW.created_at + sla_policy.response_time_target;
        ELSE
            NEW.sla_response_due := NEW.created_at + sla_policy.response_time_target;
        END IF;

        -- Calculate resolution due date
        IF sla_policy.business_hours_only THEN
            -- TODO: Implement business hours calculation
            NEW.sla_resolution_due := NEW.created_at + sla_policy.resolution_time_target;
        ELSE
            NEW.sla_resolution_due := NEW.created_at + sla_policy.resolution_time_target;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER calculate_sla_due_dates_trigger
    BEFORE INSERT ON tickets
    FOR EACH ROW
    EXECUTE FUNCTION calculate_sla_due_dates();

-- =============================================================================
-- SAMPLE DATA FOR DEVELOPMENT
-- =============================================================================

-- Insert sample organization
INSERT INTO organizations (id, name, domain) VALUES
('********-0000-0000-0000-********0001', 'Acme Corporation', 'acme.com');

-- Insert sample departments
INSERT INTO departments (id, organization_id, name, code, location) VALUES
('********-0000-0000-0000-********0011', '********-0000-0000-0000-********0001', 'Information Technology', 'IT', 'HQ Building A'),
('********-0000-0000-0000-********0012', '********-0000-0000-0000-********0001', 'Human Resources', 'HR', 'HQ Building B'),
('********-0000-0000-0000-********0013', '********-0000-0000-0000-********0001', 'Finance', 'FIN', 'HQ Building C'),
('********-0000-0000-0000-********0014', '********-0000-0000-0000-********0001', 'Marketing', 'MKT', 'HQ Building D');

-- Insert sample users
INSERT INTO users (id, organization_id, department_id, user_id, email, display_name, first_name, last_name, role) VALUES
('********-0000-0000-0000-********0021', '********-0000-0000-0000-********0001', '********-0000-0000-0000-********0011', '<EMAIL>', '<EMAIL>', 'System Administrator', 'System', 'Administrator', 'admin'),
('********-0000-0000-0000-********0022', '********-0000-0000-0000-********0001', '********-0000-0000-0000-********0011', '<EMAIL>', '<EMAIL>', 'John Smith', 'John', 'Smith', 'agent'),
('********-0000-0000-0000-********0023', '********-0000-0000-0000-********0001', '********-0000-0000-0000-********0011', '<EMAIL>', '<EMAIL>', 'Jane Doe', 'Jane', 'Doe', 'agent'),
('********-0000-0000-0000-********0024', '********-0000-0000-0000-********0001', '********-0000-0000-0000-********0012', '<EMAIL>', '<EMAIL>', 'Bob Johnson', 'Bob', 'Johnson', 'user'),
('********-0000-0000-0000-********0025', '********-0000-0000-0000-********0001', '********-0000-0000-0000-********0013', '<EMAIL>', '<EMAIL>', 'Alice Brown', 'Alice', 'Brown', 'user');

-- Insert sample service categories
INSERT INTO service_categories (id, organization_id, name, code, description) VALUES
('********-0000-0000-0000-********0031', '********-0000-0000-0000-********0001', 'Password & Access', 'PASSWORD', 'Password resets, account lockouts, access requests'),
('********-0000-0000-0000-********0032', '********-0000-0000-0000-********0001', 'Email & Communication', 'EMAIL', 'Email issues, Outlook problems, communication tools'),
('********-0000-0000-0000-********0033', '********-0000-0000-0000-********0001', 'Software & Applications', 'SOFTWARE', 'Software installation, application issues, licensing'),
('********-0000-0000-0000-********0034', '********-0000-0000-0000-********0001', 'Hardware & Equipment', 'HARDWARE', 'Computer issues, printer problems, hardware requests'),
('********-0000-0000-0000-********0035', '********-0000-0000-0000-********0001', 'Network & Connectivity', 'NETWORK', 'Internet issues, VPN problems, network access');

-- Insert sample subcategories
INSERT INTO service_subcategories (id, category_id, name, code, estimated_resolution_time) VALUES
('********-0000-0000-0000-********0041', '********-0000-0000-0000-********0031', 'Password Reset', 'PWD_RESET', '15 minutes'),
('********-0000-0000-0000-********0042', '********-0000-0000-0000-********0031', 'Account Unlock', 'ACC_UNLOCK', '10 minutes'),
('********-0000-0000-0000-********0043', '********-0000-0000-0000-********0032', 'Outlook Issues', 'OUTLOOK', '30 minutes'),
('********-0000-0000-0000-********0044', '********-0000-0000-0000-********0033', 'Software Installation', 'SW_INSTALL', '45 minutes'),
('********-0000-0000-0000-********0045', '********-0000-0000-0000-********0034', 'Printer Issues', 'PRINTER', '20 minutes');

-- Insert sample ticket priorities
INSERT INTO ticket_priorities (id, organization_id, name, code, level, sla_response_time, sla_resolution_time) VALUES
('********-0000-0000-0000-********0051', '********-0000-0000-0000-********0001', 'Low', 'LOW', 1, '4 hours', '3 days'),
('********-0000-0000-0000-********0052', '********-0000-0000-0000-********0001', 'Medium', 'MEDIUM', 2, '2 hours', '1 day'),
('********-0000-0000-0000-********0053', '********-0000-0000-0000-********0001', 'High', 'HIGH', 3, '1 hour', '4 hours'),
('********-0000-0000-0000-********0054', '********-0000-0000-0000-********0001', 'Critical', 'CRITICAL', 4, '30 minutes', '2 hours'),
('********-0000-0000-0000-********0055', '********-0000-0000-0000-********0001', 'Emergency', 'EMERGENCY', 5, '15 minutes', '1 hour');

-- Insert sample ticket statuses
INSERT INTO ticket_statuses (id, organization_id, name, code, category) VALUES
('********-0000-0000-0000-********0061', '********-0000-0000-0000-********0001', 'New', 'NEW', 'open'),
('********-0000-0000-0000-********0062', '********-0000-0000-0000-********0001', 'In Progress', 'IN_PROGRESS', 'in_progress'),
('********-0000-0000-0000-********0063', '********-0000-0000-0000-********0001', 'Pending User', 'PENDING_USER', 'pending'),
('********-0000-0000-0000-********0064', '********-0000-0000-0000-********0001', 'Resolved', 'RESOLVED', 'resolved'),
('********-0000-0000-0000-********0065', '********-0000-0000-0000-********0001', 'Closed', 'CLOSED', 'closed');

-- Insert sample SLA policy
INSERT INTO sla_policies (id, organization_id, name, response_time_target, resolution_time_target, is_default) VALUES
('********-0000-0000-0000-********0071', '********-0000-0000-0000-********0001', 'Standard SLA', '2 hours', '1 day', TRUE);

-- Insert sample automation rules
INSERT INTO automation_rules (id, organization_id, name, rule_type, conditions, actions) VALUES
('********-0000-0000-0000-********0081', '********-0000-0000-0000-********0001', 'Auto-assign Password Resets', 'auto_assign',
 '{"category_code": "PASSWORD", "subcategory_code": "PWD_RESET"}',
 '{"assign_to": "<EMAIL>", "add_note": "Auto-assigned password reset request"}'),
('********-0000-0000-0000-********0082', '********-0000-0000-0000-********0001', 'Escalate Critical Issues', 'auto_escalate',
 '{"priority_level": 4, "no_response_time": "30 minutes"}',
 '{"escalate_to": "supervisor", "notify": ["<EMAIL>"], "priority_level": 5}');

-- =============================================================================
-- VIEWS FOR COMMON QUERIES
-- =============================================================================

-- Comprehensive ticket view with all related data
CREATE VIEW v_tickets_detailed AS
SELECT
    t.id,
    t.ticket_number,
    t.subject,
    t.description,

    -- Status and Priority
    ts.name as status_name,
    ts.category as status_category,
    tp.name as priority_name,
    tp.level as priority_level,

    -- Categories
    sc.name as category_name,
    sc.code as category_code,
    ssc.name as subcategory_name,
    ssc.code as subcategory_code,

    -- Requester
    t.requester_name,
    t.requester_email,
    rd.name as requester_department,

    -- Assignment
    au.display_name as assigned_to_name,
    au.email as assigned_to_email,
    t.assigned_group,

    -- Timing
    t.created_at,
    t.updated_at,
    t.resolved_at,
    t.closed_at,
    t.sla_response_due,
    t.sla_resolution_due,
    t.first_response_at,

    -- SLA Status
    CASE
        WHEN t.first_response_at IS NULL AND t.sla_response_due < CURRENT_TIMESTAMP THEN TRUE
        ELSE FALSE
    END as sla_response_breached,

    CASE
        WHEN t.resolved_at IS NULL AND t.sla_resolution_due < CURRENT_TIMESTAMP THEN TRUE
        ELSE FALSE
    END as sla_resolution_breached,

    -- AI and Automation
    t.ai_generated,
    t.ai_confidence_score,
    t.auto_resolved,

    -- Satisfaction
    t.satisfaction_rating,
    t.satisfaction_feedback,

    -- Organization
    o.name as organization_name

FROM tickets t
LEFT JOIN ticket_statuses ts ON t.status_id = ts.id
LEFT JOIN ticket_priorities tp ON t.priority_id = tp.id
LEFT JOIN service_categories sc ON t.category_id = sc.id
LEFT JOIN service_subcategories ssc ON t.subcategory_id = ssc.id
LEFT JOIN departments rd ON t.requester_id = rd.id
LEFT JOIN users au ON t.assigned_to_id = au.id
LEFT JOIN organizations o ON t.organization_id = o.id;

-- Agent workload view
CREATE VIEW v_agent_workload AS
SELECT
    u.id as agent_id,
    u.display_name as agent_name,
    u.email as agent_email,
    d.name as department_name,

    COUNT(CASE WHEN ts.category IN ('open', 'in_progress') THEN 1 END) as active_tickets,
    COUNT(CASE WHEN ts.category = 'open' THEN 1 END) as new_tickets,
    COUNT(CASE WHEN ts.category = 'in_progress' THEN 1 END) as in_progress_tickets,
    COUNT(CASE WHEN t.sla_response_due < CURRENT_TIMESTAMP AND t.first_response_at IS NULL THEN 1 END) as overdue_response,
    COUNT(CASE WHEN t.sla_resolution_due < CURRENT_TIMESTAMP AND t.resolved_at IS NULL THEN 1 END) as overdue_resolution,

    AVG(CASE WHEN t.resolved_at IS NOT NULL THEN EXTRACT(EPOCH FROM (t.resolved_at - t.created_at))/3600 END) as avg_resolution_hours

FROM users u
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN tickets t ON u.id = t.assigned_to_id
LEFT JOIN ticket_statuses ts ON t.status_id = ts.id
WHERE u.role = 'agent' AND u.is_active = TRUE
GROUP BY u.id, u.display_name, u.email, d.name;

-- Daily performance metrics view
CREATE VIEW v_daily_performance AS
SELECT
    DATE(t.created_at) as date,
    o.name as organization_name,
    d.name as department_name,
    sc.name as category_name,

    COUNT(*) as tickets_created,
    COUNT(CASE WHEN t.resolved_at IS NOT NULL THEN 1 END) as tickets_resolved,
    COUNT(CASE WHEN t.ai_generated = TRUE THEN 1 END) as ai_generated_tickets,
    COUNT(CASE WHEN t.auto_resolved = TRUE THEN 1 END) as auto_resolved_tickets,

    AVG(CASE WHEN t.first_response_at IS NOT NULL THEN EXTRACT(EPOCH FROM (t.first_response_at - t.created_at))/60 END) as avg_first_response_minutes,
    AVG(CASE WHEN t.resolved_at IS NOT NULL THEN EXTRACT(EPOCH FROM (t.resolved_at - t.created_at))/3600 END) as avg_resolution_hours,
    AVG(t.satisfaction_rating) as avg_satisfaction_rating,

    COUNT(CASE WHEN t.sla_response_due < t.first_response_at OR (t.sla_response_due < CURRENT_TIMESTAMP AND t.first_response_at IS NULL) THEN 1 END) as sla_response_breaches,
    COUNT(CASE WHEN t.sla_resolution_due < t.resolved_at OR (t.sla_resolution_due < CURRENT_TIMESTAMP AND t.resolved_at IS NULL) THEN 1 END) as sla_resolution_breaches

FROM tickets t
LEFT JOIN organizations o ON t.organization_id = o.id
LEFT JOIN departments d ON t.requester_id = d.id
LEFT JOIN service_categories sc ON t.category_id = sc.id
GROUP BY DATE(t.created_at), o.name, d.name, sc.name
ORDER BY date DESC;

-- Knowledge base effectiveness view
CREATE VIEW v_knowledge_effectiveness AS
SELECT
    ka.id,
    ka.title,
    ka.category_id,
    sc.name as category_name,
    ka.view_count,
    ka.helpful_votes,
    ka.unhelpful_votes,

    CASE
        WHEN (ka.helpful_votes + ka.unhelpful_votes) > 0
        THEN ROUND((ka.helpful_votes::NUMERIC / (ka.helpful_votes + ka.unhelpful_votes)) * 100, 2)
        ELSE 0
    END as helpfulness_percentage,

    COUNT(kau.id) as total_usage_events,
    COUNT(CASE WHEN kau.resolved_issue = TRUE THEN 1 END) as successful_resolutions,

    ka.created_at,
    ka.last_reviewed,
    ka.review_due_date,

    CASE
        WHEN ka.review_due_date < CURRENT_DATE THEN TRUE
        ELSE FALSE
    END as review_overdue

FROM knowledge_articles ka
LEFT JOIN service_categories sc ON ka.category_id = sc.id
LEFT JOIN knowledge_article_usage kau ON ka.id = kau.article_id
WHERE ka.status = 'published'
GROUP BY ka.id, ka.title, ka.category_id, sc.name, ka.view_count, ka.helpful_votes, ka.unhelpful_votes, ka.created_at, ka.last_reviewed, ka.review_due_date
ORDER BY helpfulness_percentage DESC, view_count DESC;