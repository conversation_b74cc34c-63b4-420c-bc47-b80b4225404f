-- Database: udp_dw_dev
-- Schema: public

-- Table: activity
CREATE TABLE activity (
    -- Structure not provided in the prompt
);

-- Table: cfg_holiday
CREATE TABLE cfg_holiday (
    -- Structure not provided in the prompt
);

-- Table: cfg_service_hours
CREATE TABLE cfg_service_hours (
    -- Structure not provided in the prompt
);

-- Table: total_tickets
CREATE TABLE total_tickets (
    -- Structure not provided in the prompt
);

-- Table: z_products
CREATE TABLE z_products (
    productid integer,
    productname character varying(255),
    category character varying(255),
    price numeric(10,2)
);

-- Table: z_test
CREATE TABLE z_test (
    id integer NOT NULL GENERATED ALWAYS AS IDENTITY,
    name character varying,
    CONSTRAINT test_pk PRIMARY KEY (id)
);

-- Table: z_test_total
CREATE TABLE z_test_total (
    id integer NOT NULL,
    requested_for_name character varying(100),
    subject text,
    category character varying(100),
    sub_category character varying(100),
    status character varying(50),
    type character varying(100),
    impact character varying(50),
    urgency character varying(50),
    created_date timestamp without time zone,
    closed_date timestamp without time zone,
    last_updated_date timestamp without time zone,
    avg_response_time interval,
    agent_name character varying(100),
    agent_group_name character varying(100),
    it_asset_cost numeric(12,2),
    cost_currency character varying(10),
    department_company character varying(100),
    requester_location character varying(100),
    tag_id character varying(50),
    item_category character varying(50),
    CONSTRAINT tickets_pkey PRIMARY KEY (id)
);

-- Table: z_tk
CREATE TABLE z_tk (
    id text,
    subject text,
    status text,
    category text,
    created_date timestamp without time zone,
    avg_response_time text,
    closed_date timestamp without time zone,
    agent_name text,
    agent_group_name text,
    department_company text,
    requested_for_name text,
    last_updated_date timestamp without time zone,
    urgency text,
    impact text,
    it_asset_cost numeric,
    sub_category text,
    "tag_id (hardware/software)" text,
    requester_location text,
    item_category text,
    type text,
    cost_currency text
);

-- Table: z_ze
CREATE TABLE z_ze (
    activity_id bigint NOT NULL GENERATED ALWAYS AS IDENTITY,
    actor_id bigint,
    actor_name character varying,
    content character varying,
    sub_contents character varying,
    created_at timestamp without time zone,
    ticket_id character varying,
    CONSTRAINT z_ze_pk PRIMARY KEY (activity_id)
);

-- Table: z_ze_old
CREATE TABLE z_ze_old (
    activity_id bigint NOT NULL,
    actor_id bigint,
    actor_name character varying,
    content character varying,
    sub_contents character varying,
    created_at timestamp without time zone,
    ticket_id character varying
);