# Minimal requirements for Enterprise Technical Support Platform
# These are the absolute minimum packages needed to run the platform

# Core web framework
fastapi>=0.68.0
uvicorn>=0.15.0

# Dashboard
streamlit>=1.0.0

# Basic utilities
pydantic>=1.8.0
python-multipart>=0.0.5

# Optional but recommended (install if network allows)
# requests>=2.25.0
# pandas>=1.3.0
# plotly>=5.0.0

# Database drivers (choose one based on your setup)
# For PostgreSQL (if available):
# psycopg2-binary>=2.9.0

# For SQLite (fallback, built into Python):
# No additional package needed

# For development/testing
# pytest>=6.0.0
# httpx>=0.24.0
