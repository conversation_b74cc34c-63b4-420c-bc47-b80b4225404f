"""
Freshworks/Freshservice Integration Module
Professional integration with Freshservice API v2 for enterprise ticket management
"""

import asyncio
import logging
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import httpx
from pydantic import BaseModel, Field, validator
from sqlalchemy.orm import Session
import os
from enum import Enum

logger = logging.getLogger(__name__)

class FreshserviceConfig:
    """Configuration for Freshservice API integration"""
    
    def __init__(self):
        self.domain = os.getenv("FRESHSERVICE_DOMAIN", "your-domain.freshservice.com")
        self.api_key = os.getenv("FRESHSERVICE_API_KEY", "")
        self.api_version = "v2"
        self.base_url = f"https://{self.domain}/api/{self.api_version}"
        self.timeout = 30
        self.rate_limit_per_minute = 3000
        self.rate_limit_per_hour = 50000

class TicketPriority(str, Enum):
    LOW = "1"
    MEDIUM = "2"
    HIGH = "3"
    URGENT = "4"

class TicketStatus(str, Enum):
    OPEN = "2"
    PENDING = "3"
    RESOLVED = "4"
    CLOSED = "5"

class TicketSource(str, Enum):
    EMAIL = "1"
    PORTAL = "2"
    PHONE = "3"
    CHAT = "4"
    FEEDBACK_WIDGET = "5"
    OUTBOUND_EMAIL = "6"
    ECOMMERCE = "7"

class FreshserviceTicket(BaseModel):
    """Freshservice ticket model matching API structure"""
    
    # Core fields
    id: Optional[int] = None
    display_id: Optional[int] = None
    subject: str = Field(..., max_length=255)
    description: str = Field(..., description="HTML content allowed")
    description_text: Optional[str] = None
    
    # Categorization
    category: Optional[str] = None
    sub_category: Optional[str] = None
    item_category: Optional[str] = None
    
    # Priority and Status
    priority: TicketPriority = TicketPriority.MEDIUM
    status: TicketStatus = TicketStatus.OPEN
    source: TicketSource = TicketSource.CHAT
    
    # Requester Information
    requester_id: Optional[int] = None
    requester_email: Optional[str] = None
    requester_phone: Optional[str] = None
    
    # Assignment
    responder_id: Optional[int] = None
    group_id: Optional[int] = None
    
    # Custom fields for AI integration
    custom_fields: Dict[str, Any] = Field(default_factory=dict)
    
    # Timestamps
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    due_by: Optional[datetime] = None
    fr_due_by: Optional[datetime] = None  # First Response Due By
    
    # AI-specific metadata
    ai_generated: bool = True
    confidence_score: Optional[float] = None
    chat_session_id: Optional[str] = None
    conversation_summary: Optional[str] = None
    escalation_reason: Optional[str] = None
    
    @validator('description')
    def validate_description(cls, v):
        if len(v.strip()) < 10:
            raise ValueError('Description must be at least 10 characters')
        return v

class FreshserviceNote(BaseModel):
    """Freshservice ticket note/conversation model"""
    
    body: str = Field(..., description="Note content (HTML allowed)")
    private: bool = False
    notify_emails: List[str] = Field(default_factory=list)
    user_id: Optional[int] = None
    
class FreshserviceRequester(BaseModel):
    """Freshservice requester/user model"""
    
    first_name: str
    last_name: Optional[str] = ""
    email: str
    phone: Optional[str] = None
    mobile: Optional[str] = None
    job_title: Optional[str] = None
    department_names: List[str] = Field(default_factory=list)
    location_name: Optional[str] = None
    background_information: Optional[str] = None

class FreshserviceIntegration:
    """Professional Freshservice API integration class"""
    
    def __init__(self, config: Optional[FreshserviceConfig] = None):
        self.config = config or FreshserviceConfig()
        self.session: Optional[httpx.AsyncClient] = None
        self.rate_limiter = AsyncRateLimiter(
            self.config.rate_limit_per_minute,
            self.config.rate_limit_per_hour
        )
        
    async def __aenter__(self):
        """Async context manager entry"""
        auth = httpx.BasicAuth(self.config.api_key, "X")
        self.session = httpx.AsyncClient(
            base_url=self.config.base_url,
            auth=auth,
            timeout=self.config.timeout,
            headers={
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "AI-Support-Chatbot/1.0"
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.aclose()
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Make authenticated API request with rate limiting"""
        
        await self.rate_limiter.acquire()
        
        try:
            response = await self.session.request(
                method=method,
                url=endpoint,
                json=data,
                params=params
            )
            
            # Log rate limit headers
            self._log_rate_limit_info(response.headers)
            
            if response.status_code >= 400:
                error_detail = await self._parse_error_response(response)
                raise FreshserviceAPIError(
                    f"API request failed: {response.status_code}",
                    status_code=response.status_code,
                    error_detail=error_detail
                )
            
            return response.json()
            
        except httpx.RequestError as e:
            logger.error(f"Network error in Freshservice API: {e}")
            raise FreshserviceAPIError(f"Network error: {e}")
    
    def _log_rate_limit_info(self, headers: Dict[str, str]):
        """Log rate limit information from response headers"""
        rate_info = {
            "total": headers.get("X-Ratelimit-Total"),
            "remaining": headers.get("X-Ratelimit-Remaining"),
            "used": headers.get("X-Ratelimit-Used-CurrentRequest")
        }
        
        if any(rate_info.values()):
            logger.info(f"Freshservice Rate Limit: {rate_info}")
    
    async def _parse_error_response(self, response: httpx.Response) -> Dict[str, Any]:
        """Parse error response from Freshservice API"""
        try:
            return response.json()
        except:
            return {"message": response.text}
    
    # TICKET OPERATIONS
    
    async def create_ticket(self, ticket: FreshserviceTicket) -> Dict[str, Any]:
        """Create a new ticket in Freshservice"""
        
        logger.info(f"Creating Freshservice ticket: {ticket.subject}")
        
        # Prepare ticket data
        ticket_data = {
            "subject": ticket.subject,
            "description": ticket.description,
            "priority": int(ticket.priority),
            "status": int(ticket.status),
            "source": int(ticket.source),
            "custom_fields": {
                "ai_generated": ticket.ai_generated,
                "confidence_score": ticket.confidence_score,
                "chat_session_id": ticket.chat_session_id,
                "conversation_summary": ticket.conversation_summary,
                **ticket.custom_fields
            }
        }
        
        # Add optional fields
        if ticket.requester_email:
            ticket_data["email"] = ticket.requester_email
        if ticket.requester_phone:
            ticket_data["phone"] = ticket.requester_phone
        if ticket.category:
            ticket_data["category"] = ticket.category
        if ticket.sub_category:
            ticket_data["sub_category"] = ticket.sub_category
        if ticket.group_id:
            ticket_data["group_id"] = ticket.group_id
        if ticket.responder_id:
            ticket_data["responder_id"] = ticket.responder_id
        
        result = await self._make_request("POST", "/tickets", {"ticket": ticket_data})
        
        logger.info(f"Created Freshservice ticket #{result['ticket']['display_id']}")
        return result
    
    async def get_ticket(self, ticket_id: int, include: Optional[List[str]] = None) -> Dict[str, Any]:
        """Retrieve a ticket by ID"""
        
        params = {}
        if include:
            params["include"] = ",".join(include)
        
        result = await self._make_request("GET", f"/tickets/{ticket_id}", params=params)
        return result
    
    async def update_ticket(
        self, 
        ticket_id: int, 
        updates: Dict[str, Any],
        note: Optional[str] = None
    ) -> Dict[str, Any]:
        """Update an existing ticket"""
        
        logger.info(f"Updating Freshservice ticket #{ticket_id}")
        
        ticket_data = {"ticket": updates}
        
        # Add note if provided
        if note:
            ticket_data["ticket"]["notes"] = [{
                "body": note,
                "private": False
            }]
        
        result = await self._make_request("PUT", f"/tickets/{ticket_id}", ticket_data)
        return result
    
    async def add_note_to_ticket(
        self, 
        ticket_id: int, 
        note: FreshserviceNote
    ) -> Dict[str, Any]:
        """Add a note/conversation to a ticket"""
        
        logger.info(f"Adding note to Freshservice ticket #{ticket_id}")
        
        note_data = {
            "note": {
                "body": note.body,
                "private": note.private,
                "notify_emails": note.notify_emails
            }
        }
        
        if note.user_id:
            note_data["note"]["user_id"] = note.user_id
        
        result = await self._make_request("POST", f"/tickets/{ticket_id}/notes", note_data)
        return result
    
    async def list_tickets(
        self,
        page: int = 1,
        per_page: int = 30,
        filters: Optional[Dict[str, Any]] = None,
        updated_since: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """List tickets with optional filtering"""
        
        params = {
            "page": page,
            "per_page": per_page
        }
        
        if updated_since:
            params["updated_since"] = updated_since.isoformat()
        
        if filters:
            # Add filter parameters based on Freshservice API
            for key, value in filters.items():
                params[key] = value
        
        result = await self._make_request("GET", "/tickets", params=params)
        return result
    
    # REQUESTER OPERATIONS
    
    async def create_requester(self, requester: FreshserviceRequester) -> Dict[str, Any]:
        """Create a new requester/user"""
        
        logger.info(f"Creating Freshservice requester: {requester.email}")
        
        requester_data = {
            "requester": {
                "first_name": requester.first_name,
                "last_name": requester.last_name,
                "email": requester.email
            }
        }
        
        # Add optional fields
        optional_fields = [
            "phone", "mobile", "job_title", "department_names", 
            "location_name", "background_information"
        ]
        
        for field in optional_fields:
            value = getattr(requester, field, None)
            if value:
                requester_data["requester"][field] = value
        
        result = await self._make_request("POST", "/requesters", requester_data)
        return result
    
    async def get_requester_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Find requester by email address"""
        
        try:
            result = await self._make_request("GET", f"/requesters", params={"email": email})
            if result.get("requesters"):
                return result["requesters"][0]
            return None
        except FreshserviceAPIError as e:
            if e.status_code == 404:
                return None
            raise
    
    # ADVANCED INTEGRATION METHODS
    
    async def create_ai_ticket_from_chat(
        self,
        session_id: str,
        user_email: str,
        user_name: str,
        chat_summary: str,
        issue_category: str,
        priority: str = "medium",
        confidence_score: float = 0.0,
        department: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create ticket from AI chat conversation with enhanced metadata"""
        
        # Ensure requester exists
        requester = await self.get_requester_by_email(user_email)
        if not requester:
            # Create new requester
            new_requester = FreshserviceRequester(
                first_name=user_name.split()[0],
                last_name=" ".join(user_name.split()[1:]) if len(user_name.split()) > 1 else "",
                email=user_email,
                department_names=[department] if department else []
            )
            requester_result = await self.create_requester(new_requester)
            requester_id = requester_result["requester"]["id"]
        else:
            requester_id = requester["id"]
        
        # Create ticket with AI metadata
        ticket = FreshserviceTicket(
            subject=f"AI Support Request - {issue_category}",
            description=self._format_ai_ticket_description(chat_summary, confidence_score),
            priority=self._map_priority(priority),
            source=TicketSource.CHAT,
            requester_id=requester_id,
            requester_email=user_email,
            category=issue_category,
            ai_generated=True,
            confidence_score=confidence_score,
            chat_session_id=session_id,
            conversation_summary=chat_summary,
            custom_fields={
                "ai_confidence": confidence_score,
                "chat_session": session_id,
                "auto_generated": True,
                "escalation_source": "ai_chatbot"
            }
        )
        
        return await self.create_ticket(ticket)
    
    async def sync_ticket_with_local_db(
        self,
        freshservice_ticket_id: int,
        local_db: Session,
        local_ticket_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Synchronize Freshservice ticket with local database"""
        
        from sqlalchemy import text
        
        # Get ticket from Freshservice
        fs_ticket = await self.get_ticket(freshservice_ticket_id, include=["conversations"])
        ticket_data = fs_ticket["ticket"]
        
        # Update or create local ticket record
        if local_ticket_id:
            # Update existing local ticket
            update_query = text("""
                UPDATE test_tickets 
                SET 
                    external_ticket_id = :fs_id,
                    status = :status,
                    last_updated = CURRENT_TIMESTAMP,
                    integration_data = :integration_data
                WHERE id = :local_id
            """)
            
            local_db.execute(update_query, {
                "fs_id": str(freshservice_ticket_id),
                "status": self._map_freshservice_status(ticket_data["status"]),
                "local_id": local_ticket_id,
                "integration_data": json.dumps({
                    "freshservice_id": freshservice_ticket_id,
                    "display_id": ticket_data["display_id"],
                    "sync_timestamp": datetime.utcnow().isoformat(),
                    "fs_status": ticket_data["status"],
                    "fs_priority": ticket_data["priority"]
                })
            })
        else:
            # Create new local ticket record
            insert_query = text("""
                INSERT INTO test_tickets (
                    ticket_id, external_ticket_id, requester_email, requester_name,
                    subject, description, category, priority, status, source,
                    created_date, integration_data, ai_generated
                ) VALUES (
                    :ticket_id, :external_id, :email, :name, :subject, :description,
                    :category, :priority, :status, :source, :created_date, :integration_data, :ai_generated
                )
            """)
            
            local_db.execute(insert_query, {
                "ticket_id": f"FS-{ticket_data['display_id']}",
                "external_id": str(freshservice_ticket_id),
                "email": ticket_data.get("requester", {}).get("email", "<EMAIL>"),
                "name": f"{ticket_data.get('requester', {}).get('first_name', 'Unknown')} {ticket_data.get('requester', {}).get('last_name', '')}".strip(),
                "subject": ticket_data["subject"],
                "description": ticket_data["description_text"] or ticket_data["description"],
                "category": ticket_data.get("category"),
                "priority": self._map_freshservice_priority(ticket_data["priority"]),
                "status": self._map_freshservice_status(ticket_data["status"]),
                "source": "freshservice_sync",
                "created_date": datetime.fromisoformat(ticket_data["created_at"].replace("Z", "+00:00")),
                "integration_data": json.dumps({
                    "freshservice_id": freshservice_ticket_id,
                    "display_id": ticket_data["display_id"],
                    "sync_timestamp": datetime.utcnow().isoformat()
                }),
                "ai_generated": ticket_data.get("custom_fields", {}).get("ai_generated", False)
            })
        
        local_db.commit()
        
        logger.info(f"Synchronized Freshservice ticket #{freshservice_ticket_id} with local database")
        return fs_ticket
    
    def _format_ai_ticket_description(self, chat_summary: str, confidence_score: float) -> str:
        """Format ticket description with AI context"""
        
        confidence_text = "High" if confidence_score > 0.8 else "Medium" if confidence_score > 0.5 else "Low"
        
        return f"""
        <h3>AI-Generated Support Request</h3>
        <p><strong>Confidence Level:</strong> {confidence_text} ({confidence_score:.1%})</p>
        <p><strong>Generated:</strong> {datetime.utcnow().strftime('%Y-%m-%d %H:%M UTC')}</p>
        
        <h4>Conversation Summary:</h4>
        <div style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">
            {chat_summary}
        </div>
        
        <hr>
        <p><em>This ticket was automatically generated by the AI Support Assistant. 
        Please review the conversation summary and take appropriate action.</em></p>
        """
    
    def _map_priority(self, priority_str: str) -> TicketPriority:
        """Map string priority to Freshservice priority enum"""
        mapping = {
            "low": TicketPriority.LOW,
            "medium": TicketPriority.MEDIUM,
            "high": TicketPriority.HIGH,
            "urgent": TicketPriority.URGENT
        }
        return mapping.get(priority_str.lower(), TicketPriority.MEDIUM)
    
    def _map_freshservice_status(self, fs_status: int) -> str:
        """Map Freshservice status code to local status string"""
        mapping = {
            2: "open",
            3: "pending_user",
            4: "resolved",
            5: "closed"
        }
        return mapping.get(fs_status, "open")
    
    def _map_freshservice_priority(self, fs_priority: int) -> str:
        """Map Freshservice priority code to local priority string"""
        mapping = {
            1: "low",
            2: "medium", 
            3: "high",
            4: "urgent"
        }
        return mapping.get(fs_priority, "medium")

class AsyncRateLimiter:
    """Rate limiter for API requests"""
    
    def __init__(self, requests_per_minute: int, requests_per_hour: int):
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.minute_requests = []
        self.hour_requests = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire rate limit permission"""
        async with self.lock:
            now = datetime.utcnow()
            
            # Clean old requests
            minute_ago = now - timedelta(minutes=1)
            hour_ago = now - timedelta(hours=1)
            
            self.minute_requests = [req for req in self.minute_requests if req > minute_ago]
            self.hour_requests = [req for req in self.hour_requests if req > hour_ago]
            
            # Check limits
            if len(self.minute_requests) >= self.requests_per_minute:
                sleep_time = 60 - (now - self.minute_requests[0]).total_seconds()
                await asyncio.sleep(sleep_time)
                return await self.acquire()
            
            if len(self.hour_requests) >= self.requests_per_hour:
                sleep_time = 3600 - (now - self.hour_requests[0]).total_seconds()
                await asyncio.sleep(sleep_time)
                return await self.acquire()
            
            # Record request
            self.minute_requests.append(now)
            self.hour_requests.append(now)

class FreshserviceAPIError(Exception):
    """Custom exception for Freshservice API errors"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, error_detail: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.error_detail = error_detail

# Integration helper functions

async def create_ticket_from_escalation(
    escalation_data: Dict[str, Any],
    db: Session
) -> Dict[str, Any]:
    """Create Freshservice ticket from chatbot escalation"""
    
    config = FreshserviceConfig()
    
    async with FreshserviceIntegration(config) as fs:
        result = await fs.create_ai_ticket_from_chat(
            session_id=escalation_data["session_id"],
            user_email=escalation_data["user_email"],
            user_name=escalation_data["user_name"],
            chat_summary=escalation_data["issue_summary"],
            issue_category=escalation_data.get("category", "General"),
            priority=escalation_data.get("priority", "medium"),
            confidence_score=escalation_data.get("confidence_score", 0.0),
            department=escalation_data.get("department")
        )
        
        # Sync with local database
        await fs.sync_ticket_with_local_db(
            freshservice_ticket_id=result["ticket"]["id"],
            local_db=db
        )
        
        return result

async def update_ticket_from_chat_feedback(
    ticket_id: int,
    feedback_data: Dict[str, Any],
    db: Session
) -> Dict[str, Any]:
    """Update Freshservice ticket based on chat feedback"""
    
    config = FreshserviceConfig()
    
    async with FreshserviceIntegration(config) as fs:
        # Prepare update based on feedback
        updates = {}
        note_content = None
        
        if feedback_data.get("rating") is not None:
            rating = feedback_data["rating"]
            if rating <= 2:
                updates["priority"] = 3  # Increase priority for poor ratings
                note_content = f"User rated the AI response {rating}/5. May need human attention."
        
        if feedback_data.get("comment"):
            note_content = f"User feedback: {feedback_data['comment']}"
        
        # Update ticket if needed
        if updates:
            await fs.update_ticket(ticket_id, updates)
        
        # Add note if there's feedback
        if note_content:
            note = FreshserviceNote(
                body=note_content,
                private=True  # Internal note for agents
            )
            await fs.add_note_to_ticket(ticket_id, note)
        
        # Sync changes with local database
        await fs.sync_ticket_with_local_db(ticket_id, db)
        
        return {"status": "updated", "ticket_id": ticket_id}