# Enterprise Technical Support Platform - Production Deployment Guide

## 🚀 Executive Summary

This guide provides comprehensive deployment instructions for the enterprise-grade technical support platform, covering everything from local development to production-scale Kubernetes deployments.

## 📋 Prerequisites

### System Requirements
- **Python 3.9+** with pip
- **PostgreSQL 13+** with UUID extension
- **Redis 6+** for caching and session management
- **Docker 20.10+** and Docker Compose v2.0+
- **Kubernetes 1.24+** (for production clusters)
- **Git** for version control

### Hardware Requirements
- **Development**: 8GB RAM, 4 CPU cores, 50GB storage
- **Production**: 32GB+ RAM, 8+ CPU cores, 500GB+ storage
- **Database**: SSD storage recommended for optimal performance

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway    │    │   Enterprise    │
│   (Nginx/HAProxy)│◄──►│   (Kong/Traefik) │◄──►│   API Cluster   │
│   SSL/TLS       │    │   Rate Limiting  │    │   (FastAPI)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                   ┌──────────┴──────────┐
                   │                     │
            ┌──────▼──────┐    ┌─────────▼────────┐
            │ PostgreSQL  │    │ Redis Cluster    │
            │ Primary +   │    │ + Vector DB      │
            │ Replicas    │    │ (Chroma/Milvus)  │
            └─────────────┘    └──────────────────┘
                   │
            ┌──────▼──────┐
            │ Monitoring  │
            │ Stack       │
            │ (Prometheus │
            │ + Grafana)  │
            └─────────────┘
```

## 🚀 Quick Start Deployment

### 1. Environment Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd technical-support-chatbot

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install enterprise dependencies
pip install -r requirements-enterprise.txt
```

### 2. Database Setup

```bash
# Create PostgreSQL database
createdb enterprise_support

# Enable required extensions
psql enterprise_support -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";"
psql enterprise_support -c "CREATE EXTENSION IF NOT EXISTS \"pg_stat_statements\";"

# Run schema migration
psql enterprise_support < schema.sql

# Verify installation
psql enterprise_support -c "SELECT COUNT(*) FROM organizations;"
```

### 3. Environment Configuration

Create `.env` file:
```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/enterprise_support
ORGANIZATION_ID=00000000-0000-0000-0000-000000000001

# Security Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-here-minimum-32-chars
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
CORS_ORIGINS=["http://localhost:3000", "https://your-domain.com"]

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your-redis-password

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_TIMEOUT=300

# Dashboard Configuration
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=8501
DASHBOARD_THEME=light

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/var/log/enterprise-support/app.log

# AI Configuration
OPENAI_API_KEY=your-openai-api-key
AI_MODEL=gpt-4
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=2000

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
METRICS_ENABLED=true

# Performance Configuration
CACHE_TTL=300
CONNECTION_POOL_SIZE=20
MAX_OVERFLOW=30
QUERY_TIMEOUT=30

# Feature Flags
ENABLE_AI_AUTOMATION=true
ENABLE_SLA_MONITORING=true
ENABLE_ANALYTICS=true
ENABLE_AUDIT_LOGGING=true
```

### 4. Start Services

```bash
# Start Redis
redis-server --requirepass your-redis-password

# Start the API server
uvicorn backend.enterprise_api:app \
  --host 0.0.0.0 \
  --port 8000 \
  --workers 4 \
  --reload

# Start the dashboard (in another terminal)
streamlit run enterprise_dashboard.py \
  --server.port 8501 \
  --server.address 0.0.0.0

# Start background workers (optional)
celery -A backend.tasks worker --loglevel=info --concurrency=4
```

### 5. Verify Deployment

```bash
# Check API health
curl http://localhost:8000/health

# Check API documentation
curl http://localhost:8000/docs

# Check dashboard
curl http://localhost:8501

# Test ticket creation
curl -X POST "http://localhost:8000/api/v2/tickets" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer mock-jwt-token" \
  -d '{
    "subject": "Test ticket",
    "description": "Testing enterprise deployment",
    "requester_email": "<EMAIL>",
    "requester_name": "Test User",
    "priority": "medium"
  }'
```

## 🐳 Docker Deployment

### Docker Compose Setup

Create `docker-compose.enterprise.yml`:
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: enterprise_support
      POSTGRES_USER: support_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./init-extensions.sql:/docker-entrypoint-initdb.d/00-extensions.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U support_user -d enterprise_support"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    environment:
      DATABASE_URL: postgresql://support_user:${POSTGRES_PASSWORD}@postgres:5432/enterprise_support
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    environment:
      API_BASE_URL: http://api:8000/api/v2
    ports:
      - "8501:8501"
    depends_on:
      api:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - api
      - dashboard
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  nginx_logs:

networks:
  default:
    driver: bridge
```

### Dockerfiles

**Dockerfile.api:**
```dockerfile
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install Python dependencies
COPY requirements-enterprise.txt .
RUN pip install --no-cache-dir -r requirements-enterprise.txt

# Copy application code
COPY backend/ ./backend/
COPY schema.sql .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

CMD ["uvicorn", "backend.enterprise_api:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

**Dockerfile.dashboard:**
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements-enterprise.txt .
RUN pip install --no-cache-dir -r requirements-enterprise.txt

# Copy dashboard code
COPY enterprise_dashboard.py .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

EXPOSE 8501

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8501/_stcore/health || exit 1

CMD ["streamlit", "run", "enterprise_dashboard.py", "--server.port", "8501", "--server.address", "0.0.0.0"]
```

### Deploy with Docker

```bash
# Create environment file
cp .env.example .env.docker
# Edit .env.docker with your configuration

# Build and start all services
docker-compose -f docker-compose.enterprise.yml --env-file .env.docker up -d --build

# Check service status
docker-compose -f docker-compose.enterprise.yml ps

# View logs
docker-compose -f docker-compose.enterprise.yml logs -f api

# Scale API service
docker-compose -f docker-compose.enterprise.yml up -d --scale api=5

# Monitor resource usage
docker stats
```
