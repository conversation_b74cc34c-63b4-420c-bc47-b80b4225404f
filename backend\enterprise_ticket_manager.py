"""
Enterprise-Grade Ticket Management System
Advanced ticket management with AI integration, automation, and comprehensive workflow support
"""

import uuid
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum

import asyncio
import asyncpg
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel, Field, validator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# =============================================================================
# ENUMS AND CONSTANTS
# =============================================================================

class TicketPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class TicketStatus(str, Enum):
    NEW = "new"
    IN_PROGRESS = "in_progress"
    PENDING_USER = "pending_user"
    RESOLVED = "resolved"
    CLOSED = "closed"
    CANCELLED = "cancelled"

class TicketSource(str, Enum):
    WEB = "web"
    EMAIL = "email"
    PHONE = "phone"
    CHAT = "chat"
    API = "api"
    MOBILE = "mobile"
    WALK_IN = "walk_in"

class UserRole(str, Enum):
    USER = "user"
    AGENT = "agent"
    SUPERVISOR = "supervisor"
    ADMIN = "admin"
    SYSTEM = "system"

class AutomationRuleType(str, Enum):
    AUTO_ASSIGN = "auto_assign"
    AUTO_CATEGORIZE = "auto_categorize"
    AUTO_ESCALATE = "auto_escalate"
    AUTO_RESOLVE = "auto_resolve"
    AUTO_PRIORITY = "auto_priority"
    AUTO_NOTIFY = "auto_notify"
    AUTO_CLOSE = "auto_close"
    SLA_REMINDER = "sla_reminder"

# =============================================================================
# PYDANTIC MODELS FOR API
# =============================================================================

class TicketCreateRequest(BaseModel):
    """Request model for creating a new ticket"""
    subject: str = Field(..., min_length=5, max_length=500)
    description: str = Field(..., min_length=10)
    category_code: Optional[str] = None
    subcategory_code: Optional[str] = None
    priority: Optional[TicketPriority] = TicketPriority.MEDIUM
    urgency: Optional[str] = "medium"
    impact: Optional[str] = "medium"

    # Requester information
    requester_email: str = Field(..., regex=r'^[^@]+@[^@]+\.[^@]+$')
    requester_name: str = Field(..., min_length=2, max_length=255)
    requester_phone: Optional[str] = None
    requester_location: Optional[str] = None
    on_behalf_of_email: Optional[str] = None

    # Technical details
    affected_systems: List[str] = Field(default_factory=list)
    error_codes: List[str] = Field(default_factory=list)
    symptoms: List[str] = Field(default_factory=list)
    environment: str = "production"

    # Business impact
    business_impact: Optional[str] = None
    affected_users_count: int = 1
    estimated_cost: Optional[float] = None

    # Source and metadata
    source: TicketSource = TicketSource.WEB
    channel: Optional[str] = None
    session_id: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    custom_fields: Dict[str, Any] = Field(default_factory=dict)

    # AI-related fields
    ai_generated: bool = False
    ai_confidence_score: Optional[float] = None
    conversation_summary: Optional[str] = None

class TicketUpdateRequest(BaseModel):
    """Request model for updating an existing ticket"""
    subject: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TicketStatus] = None
    priority: Optional[TicketPriority] = None
    urgency: Optional[str] = None
    impact: Optional[str] = None

    # Assignment
    assigned_to_email: Optional[str] = None
    assigned_group: Optional[str] = None

    # Resolution
    resolution_notes: Optional[str] = None
    resolution_category: Optional[str] = None
    root_cause: Optional[str] = None

    # Technical updates
    affected_systems: Optional[List[str]] = None
    error_codes: Optional[List[str]] = None
    symptoms: Optional[List[str]] = None

    # Business impact
    business_impact: Optional[str] = None
    affected_users_count: Optional[int] = None
    estimated_cost: Optional[float] = None

    # Metadata
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None

class TicketResponse(BaseModel):
    """Response model for ticket data"""
    id: str
    ticket_number: str
    subject: str
    description: str
    status: str
    priority: str
    category: Optional[str] = None
    subcategory: Optional[str] = None

    # Requester
    requester_name: str
    requester_email: str
    requester_department: Optional[str] = None

    # Assignment
    assigned_to_name: Optional[str] = None
    assigned_to_email: Optional[str] = None
    assigned_group: Optional[str] = None

    # Timing
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    sla_response_due: Optional[datetime] = None
    sla_resolution_due: Optional[datetime] = None
    first_response_at: Optional[datetime] = None

    # SLA status
    sla_response_breached: bool = False
    sla_resolution_breached: bool = False

    # AI and automation
    ai_generated: bool = False
    ai_confidence_score: Optional[float] = None
    auto_resolved: bool = False

    # Satisfaction
    satisfaction_rating: Optional[int] = None
    satisfaction_feedback: Optional[str] = None

    # Metadata
    tags: List[str] = Field(default_factory=list)
    source: str
    environment: str = "production"

class ActivityCreateRequest(BaseModel):
    """Request model for creating ticket activities"""
    activity_type: str
    title: Optional[str] = None
    content: str
    content_type: str = "text"
    visibility: str = "internal"
    is_customer_visible: bool = False

    # Time tracking
    time_spent: Optional[str] = None  # ISO 8601 duration
    billable_time: Optional[str] = None
    work_type: Optional[str] = None

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict)

class AutomationRuleRequest(BaseModel):
    """Request model for automation rules"""
    name: str = Field(..., min_length=3, max_length=255)
    description: Optional[str] = None
    rule_type: AutomationRuleType
    conditions: Dict[str, Any]
    actions: Dict[str, Any]

    # Execution control
    is_active: bool = True
    execution_order: int = 0
    max_executions_per_ticket: int = 1
    cooldown_period: Optional[str] = None  # ISO 8601 duration

    # Scope
    applies_to_categories: List[str] = Field(default_factory=list)
    applies_to_priorities: List[str] = Field(default_factory=list)
    applies_to_sources: List[str] = Field(default_factory=list)
    time_restrictions: Dict[str, Any] = Field(default_factory=dict)

# =============================================================================
# ENTERPRISE TICKET MANAGER CLASS
# =============================================================================

class EnterpriseTicketManager:
    """
    Enterprise-grade ticket management system with advanced features:
    - Multi-tenant organization support
    - Advanced SLA management
    - AI-powered automation
    - Comprehensive audit trails
    - Real-time analytics
    - Workflow automation
    """

    def __init__(self, database_url: str, organization_id: str):
        self.database_url = database_url
        self.organization_id = organization_id
        self.engine = create_engine(database_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

        # Cache for frequently accessed data
        self._category_cache = {}
        self._priority_cache = {}
        self._status_cache = {}
        self._user_cache = {}

        logger.info(f"Initialized EnterpriseTicketManager for organization {organization_id}")

    def get_db(self) -> Session:
        """Get database session"""
        return self.SessionLocal()

    async def create_ticket(
        self,
        ticket_data: TicketCreateRequest,
        created_by_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a new ticket with full enterprise features
        """
        db = self.get_db()
        try:
            # Resolve category and subcategory IDs
            category_id = await self._resolve_category_id(ticket_data.category_code) if ticket_data.category_code else None
            subcategory_id = await self._resolve_subcategory_id(ticket_data.subcategory_code) if ticket_data.subcategory_code else None

            # Resolve priority and status IDs
            priority_id = await self._resolve_priority_id(ticket_data.priority.value)
            status_id = await self._resolve_status_id(TicketStatus.NEW.value)

            # Resolve requester information
            requester_id = await self._resolve_user_id(ticket_data.requester_email)
            on_behalf_of_id = await self._resolve_user_id(ticket_data.on_behalf_of_email) if ticket_data.on_behalf_of_email else None

            # Create ticket record
            ticket_id = str(uuid.uuid4())

            query = text("""
                INSERT INTO tickets (
                    id, organization_id, subject, description, category_id, subcategory_id,
                    priority_id, status_id, urgency, impact, requester_id, requester_email,
                    requester_name, requester_phone, requester_location, on_behalf_of_id,
                    affected_systems, error_codes, symptoms, environment, business_impact,
                    affected_users_count, estimated_cost, source, channel, session_id,
                    tags, custom_fields, ai_generated, ai_confidence_score, conversation_summary,
                    created_by_id
                ) VALUES (
                    :id, :organization_id, :subject, :description, :category_id, :subcategory_id,
                    :priority_id, :status_id, :urgency, :impact, :requester_id, :requester_email,

    async def update_ticket(
        self,
        ticket_id: str,
        update_data: TicketUpdateRequest,
        updated_by_id: Optional[str] = None,
        actor_name: str = "System",
        actor_email: str = "<EMAIL>"
    ) -> Dict[str, Any]:
        """
        Update an existing ticket with comprehensive change tracking
        """
        db = self.get_db()
        try:
            # Get current ticket state for change tracking
            current_ticket = await self._get_ticket_by_id(ticket_id, db)
            if not current_ticket:
                raise ValueError(f"Ticket {ticket_id} not found")

            # Build update query dynamically based on provided fields
            update_fields = []
            update_values = {'ticket_id': ticket_id, 'updated_by_id': updated_by_id}
            field_changes = {}

            # Track changes for each field
            if update_data.subject and update_data.subject != current_ticket['subject']:
                update_fields.append("subject = :subject")
                update_values['subject'] = update_data.subject
                field_changes['subject'] = {'old': current_ticket['subject'], 'new': update_data.subject}

            if update_data.description and update_data.description != current_ticket['description']:
                update_fields.append("description = :description")
                update_values['description'] = update_data.description
                field_changes['description'] = {'old': current_ticket['description'], 'new': update_data.description}

            if update_data.status:
                status_id = await self._resolve_status_id(update_data.status.value)
                if status_id != current_ticket['status_id']:
                    update_fields.append("status_id = :status_id")
                    update_values['status_id'] = status_id
                    field_changes['status'] = {'old': current_ticket['status_name'], 'new': update_data.status.value}

                    # Handle status-specific logic
                    if update_data.status == TicketStatus.RESOLVED:
                        update_fields.append("resolved_at = CURRENT_TIMESTAMP")
                        update_fields.append("resolved_by_id = :updated_by_id")
                    elif update_data.status == TicketStatus.CLOSED:
                        update_fields.append("closed_at = CURRENT_TIMESTAMP")
                        update_fields.append("closed_by_id = :updated_by_id")

            if update_data.priority:
                priority_id = await self._resolve_priority_id(update_data.priority.value)
                if priority_id != current_ticket['priority_id']:
                    update_fields.append("priority_id = :priority_id")
                    update_values['priority_id'] = priority_id
                    field_changes['priority'] = {'old': current_ticket['priority_name'], 'new': update_data.priority.value}

            if update_data.assigned_to_email:
                assigned_to_id = await self._resolve_user_id(update_data.assigned_to_email)
                if assigned_to_id != current_ticket['assigned_to_id']:
                    update_fields.append("assigned_to_id = :assigned_to_id")
                    update_fields.append("assigned_at = CURRENT_TIMESTAMP")
                    update_fields.append("assigned_by_id = :updated_by_id")
                    update_values['assigned_to_id'] = assigned_to_id
                    field_changes['assigned_to'] = {
                        'old': current_ticket['assigned_to_email'],
                        'new': update_data.assigned_to_email
                    }

            if update_data.resolution_notes:
                update_fields.append("resolution_notes = :resolution_notes")
                update_values['resolution_notes'] = update_data.resolution_notes
                field_changes['resolution_notes'] = {'old': current_ticket['resolution_notes'], 'new': update_data.resolution_notes}

            # Add other fields as needed...
            if update_data.tags is not None:
                update_fields.append("tags = :tags")
                update_values['tags'] = update_data.tags
                field_changes['tags'] = {'old': current_ticket['tags'], 'new': update_data.tags}

            if update_data.custom_fields is not None:
                update_fields.append("custom_fields = :custom_fields")
                update_values['custom_fields'] = json.dumps(update_data.custom_fields)
                field_changes['custom_fields'] = {'old': current_ticket['custom_fields'], 'new': update_data.custom_fields}

            # Always update the timestamp and version
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            update_fields.append("version = version + 1")

            if update_fields:
                query = text(f"""
                    UPDATE tickets
                    SET {', '.join(update_fields)}
                    WHERE id = :ticket_id AND organization_id = :organization_id
                    RETURNING ticket_number, updated_at, version
                """)
                update_values['organization_id'] = self.organization_id

                result = db.execute(query, update_values)
                ticket_info = result.fetchone()

                if not ticket_info:
                    raise ValueError(f"Failed to update ticket {ticket_id}")

                # Create activity for the update
                if field_changes:
                    activity_content = self._format_change_summary(field_changes)
                    await self._create_activity(
                        ticket_id=ticket_id,
                        activity_type="status_change" if "status" in field_changes else "comment",
                        actor_name=actor_name,
                        actor_email=actor_email,
                        actor_type="agent" if updated_by_id else "system",
                        title="Ticket Updated",
                        content=activity_content,
                        field_changes=field_changes,
                        is_customer_visible=True,
                        db=db
                    )

                db.commit()

                # Execute automation rules for the update
                await self._execute_automation_rules(ticket_id, "ticket_updated", db)

                # Get updated ticket data
                updated_ticket = await self._get_ticket_by_id(ticket_id, db)

                logger.info(f"Updated ticket {ticket_info.ticket_number}")
                return updated_ticket
            else:
                logger.info(f"No changes detected for ticket {ticket_id}")
                return current_ticket

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating ticket {ticket_id}: {e}")
            raise
        finally:
            db.close()

    async def get_ticket(self, ticket_id: str) -> Optional[Dict[str, Any]]:
        """Get ticket by ID with full details"""
        db = self.get_db()
        try:
            return await self._get_ticket_by_id(ticket_id, db)
        finally:
            db.close()

    async def search_tickets(
        self,
        query: Optional[str] = None,
        status: Optional[List[str]] = None,
        priority: Optional[List[str]] = None,
        category: Optional[List[str]] = None,
        assigned_to: Optional[str] = None,
        requester: Optional[str] = None,
        created_after: Optional[datetime] = None,
        created_before: Optional[datetime] = None,
        sla_breached: Optional[bool] = None,
        ai_generated: Optional[bool] = None,
        tags: Optional[List[str]] = None,
        limit: int = 50,
        offset: int = 0,
        sort_by: str = "created_at",
        sort_order: str = "DESC"
    ) -> Dict[str, Any]:
        """
        Advanced ticket search with multiple filters and full-text search
        """
        db = self.get_db()
        try:
            # Build WHERE clause dynamically
            where_conditions = ["t.organization_id = :organization_id"]
            query_params = {'organization_id': self.organization_id}

            if query:
                where_conditions.append("(t.subject ILIKE :search_query OR t.description ILIKE :search_query)")
                query_params['search_query'] = f"%{query}%"

            if status:
                where_conditions.append("ts.code = ANY(:status_codes)")
                query_params['status_codes'] = status

            if priority:
                where_conditions.append("tp.code = ANY(:priority_codes)")
                query_params['priority_codes'] = priority

            if category:
                where_conditions.append("sc.code = ANY(:category_codes)")
                query_params['category_codes'] = category

            if assigned_to:
                where_conditions.append("au.email = :assigned_to_email")
                query_params['assigned_to_email'] = assigned_to

            if requester:
                where_conditions.append("t.requester_email = :requester_email")
                query_params['requester_email'] = requester

            if created_after:
                where_conditions.append("t.created_at >= :created_after")
                query_params['created_after'] = created_after

            if created_before:
                where_conditions.append("t.created_at <= :created_before")
                query_params['created_before'] = created_before

            if sla_breached is not None:
                if sla_breached:
                    where_conditions.append("""
                        (t.sla_response_due < CURRENT_TIMESTAMP AND t.first_response_at IS NULL) OR
                        (t.sla_resolution_due < CURRENT_TIMESTAMP AND t.resolved_at IS NULL)
                    """)
                else:
                    where_conditions.append("""
                        NOT ((t.sla_response_due < CURRENT_TIMESTAMP AND t.first_response_at IS NULL) OR
                             (t.sla_resolution_due < CURRENT_TIMESTAMP AND t.resolved_at IS NULL))
                    """)

            if ai_generated is not None:
                where_conditions.append("t.ai_generated = :ai_generated")
                query_params['ai_generated'] = ai_generated

            if tags:
                where_conditions.append("t.tags && :tags")
                query_params['tags'] = tags

            # Build the main query
            search_query = text(f"""
                SELECT
                    t.id, t.ticket_number, t.subject, t.description,
                    ts.name as status_name, ts.code as status_code,
                    tp.name as priority_name, tp.code as priority_code,
                    sc.name as category_name, sc.code as category_code,
                    t.requester_name, t.requester_email,
                    au.display_name as assigned_to_name, au.email as assigned_to_email,
                    t.created_at, t.updated_at, t.resolved_at, t.closed_at,
                    t.sla_response_due, t.sla_resolution_due, t.first_response_at,
                    t.ai_generated, t.ai_confidence_score, t.auto_resolved,
                    t.satisfaction_rating, t.tags, t.source, t.environment,
                    CASE
                        WHEN t.first_response_at IS NULL AND t.sla_response_due < CURRENT_TIMESTAMP THEN TRUE
                        ELSE FALSE
                    END as sla_response_breached,
                    CASE
                        WHEN t.resolved_at IS NULL AND t.sla_resolution_due < CURRENT_TIMESTAMP THEN TRUE
                        ELSE FALSE
                    END as sla_resolution_breached
                FROM tickets t
                LEFT JOIN ticket_statuses ts ON t.status_id = ts.id
                LEFT JOIN ticket_priorities tp ON t.priority_id = tp.id
                LEFT JOIN service_categories sc ON t.category_id = sc.id
                LEFT JOIN users au ON t.assigned_to_id = au.id
                WHERE {' AND '.join(where_conditions)}
                ORDER BY t.{sort_by} {sort_order}
                LIMIT :limit OFFSET :offset
            """)

            query_params.update({
                'limit': limit,
                'offset': offset
            })

            # Execute search query
            result = db.execute(search_query, query_params)
            tickets = [dict(row) for row in result.fetchall()]

            # Get total count
            count_query = text(f"""
                SELECT COUNT(*) as total
                FROM tickets t
                LEFT JOIN ticket_statuses ts ON t.status_id = ts.id
                LEFT JOIN ticket_priorities tp ON t.priority_id = tp.id
                LEFT JOIN service_categories sc ON t.category_id = sc.id
                LEFT JOIN users au ON t.assigned_to_id = au.id
                WHERE {' AND '.join(where_conditions)}
            """)

            count_result = db.execute(count_query, {k: v for k, v in query_params.items() if k not in ['limit', 'offset']})
            total_count = count_result.fetchone().total

            return {
                'tickets': tickets,
                'total_count': total_count,
                'page_size': limit,
                'page_number': (offset // limit) + 1,
                'total_pages': (total_count + limit - 1) // limit
            }

        except Exception as e:
            logger.error(f"Error searching tickets: {e}")
            raise
        finally:
            db.close()

    # =============================================================================
    # HELPER METHODS
    # =============================================================================

    async def _get_ticket_by_id(self, ticket_id: str, db: Session) -> Optional[Dict[str, Any]]:
        """Get ticket by ID with all related data"""
        query = text("""
            SELECT
                t.id, t.ticket_number, t.subject, t.description,
                ts.id as status_id, ts.name as status_name, ts.code as status_code,
                tp.id as priority_id, tp.name as priority_name, tp.code as priority_code,
                sc.name as category_name, sc.code as category_code,
                ssc.name as subcategory_name, ssc.code as subcategory_code,
                t.requester_name, t.requester_email, t.requester_phone, t.requester_location,
                ru.display_name as requester_display_name,
                rd.name as requester_department,
                au.id as assigned_to_id, au.display_name as assigned_to_name, au.email as assigned_to_email,
                t.assigned_group,
                t.created_at, t.updated_at, t.resolved_at, t.closed_at,
                t.sla_response_due, t.sla_resolution_due, t.first_response_at,
                t.ai_generated, t.ai_confidence_score, t.auto_resolved,
                t.satisfaction_rating, t.satisfaction_feedback,
                t.tags, t.source, t.environment, t.custom_fields, t.resolution_notes,
                t.affected_systems, t.error_codes, t.symptoms,
                t.business_impact, t.affected_users_count, t.estimated_cost,
                CASE
                    WHEN t.first_response_at IS NULL AND t.sla_response_due < CURRENT_TIMESTAMP THEN TRUE
                    ELSE FALSE
                END as sla_response_breached,
                CASE
                    WHEN t.resolved_at IS NULL AND t.sla_resolution_due < CURRENT_TIMESTAMP THEN TRUE
                    ELSE FALSE
                END as sla_resolution_breached
            FROM tickets t
            LEFT JOIN ticket_statuses ts ON t.status_id = ts.id
            LEFT JOIN ticket_priorities tp ON t.priority_id = tp.id
            LEFT JOIN service_categories sc ON t.category_id = sc.id
            LEFT JOIN service_subcategories ssc ON t.subcategory_id = ssc.id
            LEFT JOIN users ru ON t.requester_id = ru.id
            LEFT JOIN departments rd ON ru.department_id = rd.id
            LEFT JOIN users au ON t.assigned_to_id = au.id
            WHERE t.id = :ticket_id AND t.organization_id = :organization_id
        """)

        result = db.execute(query, {
            'ticket_id': ticket_id,
            'organization_id': self.organization_id
        })

        row = result.fetchone()
        return dict(row) if row else None

    async def _resolve_category_id(self, category_code: str) -> Optional[str]:
        """Resolve category code to ID"""
        if category_code in self._category_cache:
            return self._category_cache[category_code]

        db = self.get_db()
        try:
            query = text("""
                SELECT id FROM service_categories
                WHERE code = :code AND organization_id = :organization_id
            """)
            result = db.execute(query, {
                'code': category_code,
                'organization_id': self.organization_id
            })
            row = result.fetchone()
            category_id = str(row.id) if row else None

            if category_id:
                self._category_cache[category_code] = category_id

            return category_id
        finally:
            db.close()

    async def _resolve_subcategory_id(self, subcategory_code: str) -> Optional[str]:
        """Resolve subcategory code to ID"""
        db = self.get_db()
        try:
            query = text("""
                SELECT ssc.id FROM service_subcategories ssc
                JOIN service_categories sc ON ssc.category_id = sc.id
                WHERE ssc.code = :code AND sc.organization_id = :organization_id
            """)
            result = db.execute(query, {
                'code': subcategory_code,
                'organization_id': self.organization_id
            })
            row = result.fetchone()
            return str(row.id) if row else None
        finally:
            db.close()

    async def _resolve_priority_id(self, priority_code: str) -> Optional[str]:
        """Resolve priority code to ID"""
        if priority_code in self._priority_cache:
            return self._priority_cache[priority_code]

        db = self.get_db()
        try:
            query = text("""
                SELECT id FROM ticket_priorities
                WHERE code = :code AND organization_id = :organization_id
            """)
            result = db.execute(query, {
                'code': priority_code.upper(),
                'organization_id': self.organization_id
            })
            row = result.fetchone()
            priority_id = str(row.id) if row else None

            if priority_id:
                self._priority_cache[priority_code] = priority_id

            return priority_id
        finally:
            db.close()

    async def _resolve_status_id(self, status_code: str) -> Optional[str]:
        """Resolve status code to ID"""
        if status_code in self._status_cache:
            return self._status_cache[status_code]

        db = self.get_db()
        try:
            query = text("""
                SELECT id FROM ticket_statuses
                WHERE code = :code AND organization_id = :organization_id
            """)
            result = db.execute(query, {
                'code': status_code.upper(),
                'organization_id': self.organization_id
            })
            row = result.fetchone()
            status_id = str(row.id) if row else None

            if status_id:
                self._status_cache[status_code] = status_id

            return status_id
        finally:
            db.close()

    async def _resolve_user_id(self, email: str) -> Optional[str]:
        """Resolve user email to ID"""
        if email in self._user_cache:
            return self._user_cache[email]

        db = self.get_db()
        try:
            query = text("""
                SELECT id FROM users
                WHERE email = :email AND organization_id = :organization_id
            """)
            result = db.execute(query, {
                'email': email,
                'organization_id': self.organization_id
            })
            row = result.fetchone()
            user_id = str(row.id) if row else None

            if user_id:
                self._user_cache[email] = user_id

            return user_id
        finally:
            db.close()

    async def _create_activity(
        self,
        ticket_id: str,
        activity_type: str,
        actor_name: str,
        actor_email: str,
        actor_type: str,
        title: Optional[str] = None,
        content: str = "",
        content_type: str = "text",
        visibility: str = "internal",
        is_customer_visible: bool = False,
        field_changes: Optional[Dict[str, Any]] = None,
        time_spent: Optional[str] = None,
        billable_time: Optional[str] = None,
        work_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        db: Optional[Session] = None
    ) -> str:
        """Create a ticket activity record"""
        should_close_db = db is None
        if db is None:
            db = self.get_db()

        try:
            activity_id = str(uuid.uuid4())

            # Resolve actor ID if possible
            actor_id = await self._resolve_user_id(actor_email)

            query = text("""
                INSERT INTO ticket_activities (
                    id, ticket_id, activity_type, actor_id, actor_name, actor_type, actor_email,
                    title, content, content_type, visibility, is_customer_visible,
                    field_changes, time_spent, billable_time, work_type, metadata
                ) VALUES (
                    :id, :ticket_id, :activity_type, :actor_id, :actor_name, :actor_type, :actor_email,
                    :title, :content, :content_type, :visibility, :is_customer_visible,
                    :field_changes, :time_spent, :billable_time, :work_type, :metadata
                )
            """)

            db.execute(query, {
                'id': activity_id,
                'ticket_id': ticket_id,
                'activity_type': activity_type,
                'actor_id': actor_id,
                'actor_name': actor_name,
                'actor_type': actor_type,
                'actor_email': actor_email,
                'title': title,
                'content': content,
                'content_type': content_type,
                'visibility': visibility,
                'is_customer_visible': is_customer_visible,
                'field_changes': json.dumps(field_changes) if field_changes else None,
                'time_spent': time_spent,
                'billable_time': billable_time,
                'work_type': work_type,
                'metadata': json.dumps(metadata) if metadata else None
            })

            if should_close_db:
                db.commit()

            return activity_id

        except Exception as e:
            if should_close_db:
                db.rollback()
            logger.error(f"Error creating activity: {e}")
            raise
        finally:
            if should_close_db:
                db.close()

    async def _execute_automation_rules(
        self,
        ticket_id: str,
        trigger_event: str,
        db: Optional[Session] = None
    ) -> List[Dict[str, Any]]:
        """Execute applicable automation rules for a ticket"""
        should_close_db = db is None
        if db is None:
            db = self.get_db()

        try:
            # Get active automation rules for this organization
            query = text("""
                SELECT ar.* FROM automation_rules ar
                WHERE ar.organization_id = :organization_id
                AND ar.is_active = TRUE
                ORDER BY ar.execution_order, ar.created_at
            """)

            result = db.execute(query, {'organization_id': self.organization_id})
            rules = [dict(row) for row in result.fetchall()]

            executed_rules = []

            for rule in rules:
                try:
                    # Check if rule should be executed
                    if await self._should_execute_rule(rule, ticket_id, trigger_event, db):
                        execution_result = await self._execute_rule(rule, ticket_id, trigger_event, db)
                        executed_rules.append(execution_result)

                        # Log rule execution
                        await self._log_rule_execution(rule['id'], ticket_id, execution_result, db)

                except Exception as e:
                    logger.error(f"Error executing automation rule {rule['id']}: {e}")
                    await self._log_rule_execution(
                        rule['id'],
                        ticket_id,
                        {'status': 'error', 'error': str(e)},
                        db
                    )
                    continue

            return executed_rules

        except Exception as e:
            logger.error(f"Error executing automation rules: {e}")
            raise
        finally:
            if should_close_db:
                db.close()

    async def _should_execute_rule(
        self,
        rule: Dict[str, Any],
        ticket_id: str,
        trigger_event: str,
        db: Session
    ) -> bool:
        """Check if an automation rule should be executed"""
        try:
            # Get ticket data
            ticket = await self._get_ticket_by_id(ticket_id, db)
            if not ticket:
                return False

            # Parse rule conditions
            conditions = rule['conditions']

            # Check basic conditions
            if 'category_code' in conditions:
                if ticket['category_code'] != conditions['category_code']:
                    return False

            if 'subcategory_code' in conditions:
                if ticket['subcategory_code'] != conditions['subcategory_code']:
                    return False

            if 'priority_level' in conditions:
                priority_query = text("""
                    SELECT level FROM ticket_priorities
                    WHERE id = :priority_id
                """)
                priority_result = db.execute(priority_query, {'priority_id': ticket['priority_id']})
                priority_row = priority_result.fetchone()
                if not priority_row or priority_row.level != conditions['priority_level']:
                    return False

            if 'status_code' in conditions:
                if ticket['status_code'] != conditions['status_code']:
                    return False

            # Check keywords in subject/description
            if 'keywords' in conditions:
                keywords = conditions['keywords']
                text_to_search = f"{ticket['subject']} {ticket['description']}".lower()
                if not any(keyword.lower() in text_to_search for keyword in keywords):
                    return False

            # Check execution limits
            if rule['max_executions_per_ticket'] > 0:
                execution_count_query = text("""
                    SELECT COUNT(*) as count FROM automation_rule_executions
                    WHERE rule_id = :rule_id AND ticket_id = :ticket_id
                    AND execution_status = 'success'
                """)
                count_result = db.execute(execution_count_query, {
                    'rule_id': rule['id'],
                    'ticket_id': ticket_id
                })
                count_row = count_result.fetchone()
                if count_row and count_row.count >= rule['max_executions_per_ticket']:
                    return False

            # Check cooldown period
            if rule['cooldown_period']:
                last_execution_query = text("""
                    SELECT executed_at FROM automation_rule_executions
                    WHERE rule_id = :rule_id AND ticket_id = :ticket_id
                    ORDER BY executed_at DESC LIMIT 1
                """)
                last_result = db.execute(last_execution_query, {
                    'rule_id': rule['id'],
                    'ticket_id': ticket_id
                })
                last_row = last_result.fetchone()
                if last_row:
                    # Parse cooldown period and check if enough time has passed
                    # This is a simplified check - in production, you'd want proper interval parsing
                    cooldown_minutes = 30  # Default cooldown
                    time_since_last = datetime.utcnow() - last_row.executed_at
                    if time_since_last.total_seconds() < (cooldown_minutes * 60):
                        return False

            return True

        except Exception as e:
            logger.error(f"Error checking rule conditions: {e}")
            return False

    async def _execute_rule(
        self,
        rule: Dict[str, Any],
        ticket_id: str,
        trigger_event: str,
        db: Session
    ) -> Dict[str, Any]:
        """Execute an automation rule"""
        try:
            actions = rule['actions']
            execution_result = {
                'rule_id': rule['id'],
                'rule_name': rule['name'],
                'status': 'success',
                'actions_taken': [],
                'execution_time': datetime.utcnow()
            }

            # Execute different types of actions
            if rule['rule_type'] == AutomationRuleType.AUTO_ASSIGN.value:
                if 'assign_to' in actions:
                    assigned_to_id = await self._resolve_user_id(actions['assign_to'])
                    if assigned_to_id:
                        update_query = text("""
                            UPDATE tickets
                            SET assigned_to_id = :assigned_to_id,
                                assigned_at = CURRENT_TIMESTAMP,
                                assigned_by_id = NULL
                            WHERE id = :ticket_id
                        """)
                        db.execute(update_query, {
                            'assigned_to_id': assigned_to_id,
                            'ticket_id': ticket_id
                        })
                        execution_result['actions_taken'].append(f"Assigned to {actions['assign_to']}")

                        # Create activity
                        await self._create_activity(
                            ticket_id=ticket_id,
                            activity_type="assignment",
                            actor_name="Automation System",
                            actor_email="<EMAIL>",
                            actor_type="system",
                            title="Auto-assigned",
                            content=f"Automatically assigned to {actions['assign_to']} by rule: {rule['name']}",
                            is_customer_visible=False,
                            db=db
                        )

            elif rule['rule_type'] == AutomationRuleType.AUTO_ESCALATE.value:
                if 'priority_level' in actions:
                    # Find priority by level
                    priority_query = text("""
                        SELECT id FROM ticket_priorities
                        WHERE level = :level AND organization_id = :organization_id
                    """)
                    priority_result = db.execute(priority_query, {
                        'level': actions['priority_level'],
                        'organization_id': self.organization_id
                    })
                    priority_row = priority_result.fetchone()

                    if priority_row:
                        update_query = text("""
                            UPDATE tickets
                            SET priority_id = :priority_id
                            WHERE id = :ticket_id
                        """)
                        db.execute(update_query, {
                            'priority_id': priority_row.id,
                            'ticket_id': ticket_id
                        })
                        execution_result['actions_taken'].append(f"Escalated to priority level {actions['priority_level']}")

                        # Create activity
                        await self._create_activity(
                            ticket_id=ticket_id,
                            activity_type="escalation",
                            actor_name="Automation System",
                            actor_email="<EMAIL>",
                            actor_type="system",
                            title="Auto-escalated",
                            content=f"Automatically escalated by rule: {rule['name']}",
                            is_customer_visible=False,
                            db=db
                        )

            # Add more rule types as needed...

            return execution_result

        except Exception as e:
            logger.error(f"Error executing rule {rule['id']}: {e}")
            return {
                'rule_id': rule['id'],
                'rule_name': rule['name'],
                'status': 'error',
                'error': str(e),
                'execution_time': datetime.utcnow()
            }

    async def _log_rule_execution(
        self,
        rule_id: str,
        ticket_id: str,
        execution_result: Dict[str, Any],
        db: Session
    ):
        """Log automation rule execution"""
        try:
            query = text("""
                INSERT INTO automation_rule_executions (
                    id, rule_id, ticket_id, execution_status,
                    actions_taken, error_message, executed_at
                ) VALUES (
                    :id, :rule_id, :ticket_id, :execution_status,
                    :actions_taken, :error_message, :executed_at
                )
            """)

            db.execute(query, {
                'id': str(uuid.uuid4()),
                'rule_id': rule_id,
                'ticket_id': ticket_id,
                'execution_status': execution_result['status'],
                'actions_taken': json.dumps(execution_result.get('actions_taken', [])),
                'error_message': execution_result.get('error'),
                'executed_at': execution_result['execution_time']
            })

        except Exception as e:
            logger.error(f"Error logging rule execution: {e}")

    def _format_change_summary(self, field_changes: Dict[str, Any]) -> str:
        """Format field changes into a readable summary"""
        changes = []
        for field, change in field_changes.items():
            old_val = change.get('old', 'None')
            new_val = change.get('new', 'None')
            changes.append(f"{field.replace('_', ' ').title()}: {old_val} → {new_val}")

        return "Changes made:\n" + "\n".join(f"• {change}" for change in changes)

            result = db.execute(query, {
                'id': ticket_id,
                'organization_id': self.organization_id,
                'subject': ticket_data.subject,
                'description': ticket_data.description,
                'category_id': category_id,
                'subcategory_id': subcategory_id,
                'priority_id': priority_id,
                'status_id': status_id,
                'urgency': ticket_data.urgency,
                'impact': ticket_data.impact,
                'requester_id': requester_id,
                'requester_email': ticket_data.requester_email,
                'requester_name': ticket_data.requester_name,
                'requester_phone': ticket_data.requester_phone,
                'requester_location': ticket_data.requester_location,
                'on_behalf_of_id': on_behalf_of_id,
                'affected_systems': ticket_data.affected_systems,
                'error_codes': ticket_data.error_codes,
                'symptoms': ticket_data.symptoms,
                'environment': ticket_data.environment,
                'business_impact': ticket_data.business_impact,
                'affected_users_count': ticket_data.affected_users_count,
                'estimated_cost': ticket_data.estimated_cost,
                'source': ticket_data.source.value,
                'channel': ticket_data.channel,
                'session_id': ticket_data.session_id,
                'tags': ticket_data.tags,
                'custom_fields': json.dumps(ticket_data.custom_fields),
                'ai_generated': ticket_data.ai_generated,
                'ai_confidence_score': ticket_data.ai_confidence_score,
                'conversation_summary': ticket_data.conversation_summary,
                'created_by_id': created_by_id
            })

            ticket_info = result.fetchone()
            db.commit()

            # Create initial activity
            await self._create_activity(
                ticket_id=ticket_id,
                activity_type="comment",
                actor_name=ticket_data.requester_name,
                actor_email=ticket_data.requester_email,
                actor_type="user",
                title="Ticket Created",
                content=f"Ticket created: {ticket_data.subject}",
                is_customer_visible=True,
                db=db
            )

            # Execute automation rules
            await self._execute_automation_rules(ticket_id, "ticket_created", db)

            # Prepare response
            ticket_response = {
                'id': ticket_id,
                'ticket_number': ticket_info.ticket_number,
                'subject': ticket_data.subject,
                'status': TicketStatus.NEW.value,
                'priority': ticket_data.priority.value,
                'requester_name': ticket_data.requester_name,
                'requester_email': ticket_data.requester_email,
                'created_at': ticket_info.created_at,
                'sla_response_due': ticket_info.sla_response_due,
                'sla_resolution_due': ticket_info.sla_resolution_due,
                'ai_generated': ticket_data.ai_generated,
                'source': ticket_data.source.value
            }

            logger.info(f"Created ticket {ticket_info.ticket_number} for {ticket_data.requester_email}")
            return ticket_response

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating ticket: {e}")
            raise
        finally:
            db.close()
