





import os
import sys
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json

# Add project root to path
project_root = Path.cwd()
if project_root.name == 'notebooks':
    project_root = project_root.parent

sys.path.append(str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(project_root / '.env')

print(f"Project root: {project_root}")
print(f"Python path updated: {str(project_root) in sys.path}")


# Import ingestion modules
from scripts.ingest.parsers import DocumentParser
from scripts.ingest.cleaner import Text<PERSON>leaner, DocumentChunker, clean_and_chunk_document
from scripts.ingest.embedder import create_embedder
from scripts.ingest.vector_store import VectorStoreManager
from scripts.ingest.run_ingestion import DocumentIngestionPipeline

print("✅ All ingestion modules imported successfully!")





# Initialize document parser
parser = DocumentParser()

# Show supported file types
supported_extensions = parser.get_supported_extensions()
print(f"📄 Supported file types: {', '.join(supported_extensions)}")

# Create a sample document for testing
sample_dir = project_root / 'sample_docs'
sample_dir.mkdir(exist_ok=True)

# Create sample text document
sample_text = """# IT Support Guide

## Password Reset Procedure

To reset your password:
1. Go to the login page
2. Click "Forgot Password"
3. Enter your email address
4. Check your email for reset instructions
5. Follow the link and create a new password

## Software Installation

For installing new software:
- Contact IT support
- Provide software name and business justification
- Wait for approval
- IT will install the software remotely

## Network Issues

Common network troubleshooting steps:
1. Check cable connections
2. Restart your computer
3. Try a different network port
4. Contact IT if problem persists
"""

sample_file = sample_dir / 'it_support_guide.txt'
with open(sample_file, 'w', encoding='utf-8') as f:
    f.write(sample_text)

print(f"✅ Created sample document: {sample_file}")


# Parse the sample document
parsed_result = parser.parse_document(sample_file)

print(f"📊 Parsing Results:")
print(f"  Success: {parsed_result['success']}")
print(f"  Title: {parsed_result['title']}")
print(f"  File type: {parsed_result['file_type']}")
print(f"  Content length: {len(parsed_result['content'])} characters")

if parsed_result['success']:
    print(f"\n📄 Content preview (first 300 chars):")
    print(parsed_result['content'][:300] + "...")
    
    print(f"\n📋 Metadata:")
    for key, value in parsed_result['metadata'].items():
        print(f"  {key}: {value}")
else:
    print(f"❌ Parsing failed: {parsed_result.get('error')}")





# Clean and chunk the document
if parsed_result['success']:
    cleaned_text, chunks, metadata = clean_and_chunk_document(
        parsed_result['content'],
        chunk_size=300,  # Smaller chunks for demo
        chunk_overlap=50,
        preserve_structure=True
    )
    
    print(f"📊 Cleaning & Chunking Results:")
    print(f"  Original length: {len(parsed_result['content'])} chars")
    print(f"  Cleaned length: {len(cleaned_text)} chars")
    print(f"  Number of chunks: {len(chunks)}")
    
    print(f"\n📋 Text Metadata:")
    for key, value in metadata.items():
        print(f"  {key}: {value}")
    
    print(f"\n📄 Sample chunks:")
    for i, chunk in enumerate(chunks[:3]):
        print(f"\nChunk {i + 1}:")
        print(f"  Length: {len(chunk.content)} chars")
        print(f"  Index: {chunk.chunk_index}")
        print(f"  Position: {chunk.start_char}-{chunk.end_char}")
        print(f"  Content: {chunk.content[:100]}...")





# Initialize embedder
embedder = create_embedder(model_name='all-MiniLM-L6-v2', batch_size=8)

# Get model information
model_info = embedder.embedding_generator.get_model_info()
print(f"🤖 Embedding Model Info:")
for key, value in model_info.items():
    print(f"  {key}: {value}")


# Convert chunks to embedder format
chunk_dicts = []
for chunk in chunks:
    chunk_dict = {
        'content': chunk.content,
        'title': parsed_result['title'],
        'source_path': str(sample_file),
        'metadata': {**chunk.metadata, **parsed_result['metadata']},
        'chunk_index': chunk.chunk_index
    }
    chunk_dicts.append(chunk_dict)

print(f"📦 Prepared {len(chunk_dicts)} chunks for embedding")


# Generate embeddings
print("⏳ Generating embeddings...")
embedded_chunks = embedder.embed_chunks(chunk_dicts)

print(f"✅ Generated embeddings for {len(embedded_chunks)} chunks")

# Show embedding info
if embedded_chunks:
    sample_chunk = embedded_chunks[0]
    print(f"\n📊 Sample embedding info:")
    print(f"  Embedding dimension: {sample_chunk.get('embedding_dimension')}")
    print(f"  Embedding model: {sample_chunk.get('embedding_model')}")
    print(f"  Content hash: {sample_chunk.get('content_hash')[:16]}...")
    print(f"  Embedding preview: {sample_chunk['embedding'][:5]}...")





# Initialize vector store
vector_store = VectorStoreManager()

# Check connection
stats = vector_store.get_stats()
print(f"🗄️  Vector Store Stats:")
for key, value in stats.items():
    print(f"  {key}: {value}")


# Store chunks in vector database
document_id = 12345  # Sample document ID

print(f"💾 Storing {len(embedded_chunks)} chunks in vector database...")
success = vector_store.store_document_chunks(embedded_chunks, document_id)

if success:
    print("✅ Chunks stored successfully!")
    
    # Get updated stats
    new_stats = vector_store.get_stats()
    print(f"\n📊 Updated stats:")
    print(f"  Total entities: {new_stats.get('total_entities', 0)}")
else:
    print("❌ Failed to store chunks")





# Test search functionality
test_queries = [
    "How do I reset my password?",
    "Installing new software",
    "Network connection problems",
    "IT support contact"
]

print("🔍 Testing vector search...")

for query in test_queries:
    print(f"\n🔎 Query: '{query}'")
    
    # Generate query embedding
    query_embedding = embedder.embedding_generator.generate_embedding(query)
    
    # Search for similar chunks
    results = vector_store.search_knowledge_base(query_embedding.tolist(), top_k=2)
    
    print(f"  Found {len(results)} results:")
    for i, result in enumerate(results, 1):
        print(f"    {i}. Score: {result['relevance_score']:.3f}")
        print(f"       Content: {result['content'][:80]}...")
        print(f"       Title: {result['title']}")





# Create additional sample documents
additional_docs = {
    'email_setup.txt': """
# Email Setup Guide

## Outlook Configuration
1. Open Outlook
2. Go to File > Account Settings
3. Click "New" to add account
4. Enter your email and password
5. Outlook will auto-configure settings

## Mobile Email Setup
- Download company email app
- Enter your credentials
- Accept security policies
- Sync will begin automatically
""",
    
    'vpn_access.txt': """
# VPN Access Instructions

## Installing VPN Client
1. Download VPN client from IT portal
2. Run installer as administrator
3. Follow installation wizard
4. Restart computer when prompted

## Connecting to VPN
- Open VPN client
- Enter your username and password
- Select company server
- Click Connect
- Verify connection status

## Troubleshooting VPN
- Check internet connection
- Verify credentials
- Try different server
- Contact IT if issues persist
"""
}

# Create the additional documents
for filename, content in additional_docs.items():
    file_path = sample_dir / filename
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"📄 Created: {filename}")

print(f"\n✅ Created {len(additional_docs)} additional documents")


# Use the complete ingestion pipeline for batch processing
pipeline = DocumentIngestionPipeline()

print("🔄 Running batch ingestion...")
results = pipeline.process_directory(str(sample_dir), recursive=False, force_reprocess=True)

print(f"\n📊 Batch Processing Results:")
print(f"  Total files: {results['total_files']}")
print(f"  Successful: {results['successful']}")
print(f"  Failed: {results['failed']}")
print(f"  Skipped: {results['skipped']}")
print(f"  Total chunks: {results['total_chunks']}")

# Show individual file results
print(f"\n📋 Individual File Results:")
for file_result in results['files']:
    status = "✅" if file_result['success'] else "❌"
    filename = Path(file_result['file_path']).name
    chunks = file_result.get('chunks_created', 0)
    time_taken = file_result.get('processing_time', 0)
    
    print(f"  {status} {filename}: {chunks} chunks ({time_taken:.2f}s)")
    
    if not file_result['success']:
        print(f"      Error: {file_result.get('error', 'Unknown error')}")





# Get final statistics
final_stats = pipeline.get_stats()

print(f"📊 Final Knowledge Base Statistics:")
print(f"  Database available: {final_stats['database_available']}")

if 'total_documents' in final_stats:
    print(f"  Total documents: {final_stats['total_documents']}")
    print(f"  Total chunks: {final_stats['total_chunks']}")

vector_stats = final_stats.get('vector_store', {})
print(f"\n🗄️  Vector Store Statistics:")
for key, value in vector_stats.items():
    print(f"  {key}: {value}")


# Create visualization of processing results
import matplotlib.pyplot as plt
import numpy as np

# Extract data for visualization
file_names = [Path(f['file_path']).name for f in results['files']]
chunk_counts = [f.get('chunks_created', 0) for f in results['files']]
processing_times = [f.get('processing_time', 0) for f in results['files']]
success_status = [f['success'] for f in results['files']]

# Create subplots
fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))

# Plot 1: Chunks per document
colors = ['green' if s else 'red' for s in success_status]
ax1.bar(range(len(file_names)), chunk_counts, color=colors)
ax1.set_title('Chunks Created per Document')
ax1.set_xlabel('Documents')
ax1.set_ylabel('Number of Chunks')
ax1.set_xticks(range(len(file_names)))
ax1.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in file_names], rotation=45)

# Plot 2: Processing times
ax2.bar(range(len(file_names)), processing_times, color='blue', alpha=0.7)
ax2.set_title('Processing Time per Document')
ax2.set_xlabel('Documents')
ax2.set_ylabel('Time (seconds)')
ax2.set_xticks(range(len(file_names)))
ax2.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in file_names], rotation=45)

# Plot 3: Success/Failure pie chart
success_counts = [sum(success_status), len(success_status) - sum(success_status)]
ax3.pie(success_counts, labels=['Success', 'Failed'], autopct='%1.1f%%', colors=['green', 'red'])
ax3.set_title('Processing Success Rate')

plt.tight_layout()
plt.show()

print(f"\n📈 Processing Summary:")
print(f"  Average chunks per document: {np.mean(chunk_counts):.1f}")
print(f"  Average processing time: {np.mean(processing_times):.2f}s")
print(f"  Success rate: {(sum(success_status)/len(success_status)*100):.1f}%")


# Test the complete knowledge base with comprehensive search
comprehensive_queries = [
    "How to reset password?",
    "Email setup instructions",
    "VPN connection troubleshooting",
    "Software installation process",
    "Network connectivity issues"
]

print("🔍 Comprehensive Knowledge Base Search Test:")
print("=" * 50)

for query in comprehensive_queries:
    print(f"\n🔎 Query: '{query}'")
    
    # Generate query embedding
    query_embedding = embedder.embedding_generator.generate_embedding(query)
    
    # Search for similar chunks
    results = vector_store.search_knowledge_base(query_embedding.tolist(), top_k=3)
    
    if results:
        print(f"  📊 Found {len(results)} relevant chunks:")
        for i, result in enumerate(results, 1):
            print(f"    {i}. Score: {result['relevance_score']:.3f} | Source: {Path(result['source_path']).name}")
            print(f"       {result['content'][:100]}...")
    else:
        print(f"  ❌ No results found")

print(f"\n✅ Knowledge base is ready for production use!")





# Cleanup
vector_store.close()
pipeline.cleanup()

print("🧹 Cleanup completed!")
print("\n🎉 Document ingestion pipeline demonstration completed successfully!")
print("\nNext: Use notebook 03_RAG_Pipeline_Implementation.ipynb to test the complete RAG system.")
