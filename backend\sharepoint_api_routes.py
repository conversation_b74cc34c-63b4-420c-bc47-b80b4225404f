"""
Enhanced API Routes for SharePoint Integration
Combines user and staff assistant APIs with Freshservice integration
"""

from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, List, Any, Optional
import json
import logging
from datetime import datetime, timedelta

from .database import get_db
from .rag_pipeline import RAGPipeline
from .sharepoint_user_assistant import (
    SharePointUserAssistant, UserQuery, AIResponse, EscalationRequest
)
from .sharepoint_staff_assistant import (
    SharePointStaffAssistant, TicketAnalysisRequest, StaffFeedback, 
    KnowledgeUpdate, TicketAnalysis
)
from .freshworks_integration import (
    FreshserviceIntegration, FreshserviceConfig, create_ticket_from_escalation,
    update_ticket_from_chat_feedback
)

logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Router setup
sharepoint_router = APIRouter(prefix="/api/v1/sharepoint", tags=["SharePoint Integration"])

# Initialize components
rag_pipeline = RAGPipeline()
user_assistant = SharePointUserAssistant(rag_pipeline)
staff_assistant = SharePointStaffAssistant(rag_pipeline)

# Import authentication
from .azure_auth import (
    get_current_user, get_current_user_dev, require_permission, require_role,
    Permissions, Roles, UserInfo
)

# Use development mode if enabled
if os.getenv("DEV_MODE", "false").lower() == "true":
    get_authenticated_user = get_current_user_dev
else:
    get_authenticated_user = get_current_user

# User Assistant Routes
@sharepoint_router.post("/user-assistant/query", response_model=AIResponse)
async def process_user_query(
    query: UserQuery,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: UserInfo = Depends(require_permission(Permissions.USER_ASSISTANT_USE))
):
    """Process user query through AI assistant"""
    
    try:
        # Log the query for analytics
        background_tasks.add_task(log_user_query, query, db)
        
        # Process the query
        response = await user_assistant.process_user_query(query, db)
        
        # Log the response for improvement
        background_tasks.add_task(log_ai_response, query, response, db)
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing user query: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process query"
        )

@sharepoint_router.post("/user-assistant/escalate")
async def escalate_to_support(
    escalation: EscalationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: UserInfo = Depends(require_permission(Permissions.USER_ASSISTANT_USE))
):
    """Escalate user issue to human support"""
    
    try:
        result = await user_assistant.escalate_to_human_support(escalation, db)
        
        # Log escalation for analytics
        background_tasks.add_task(log_escalation, escalation, result, db)
        
        return result
        
    except Exception as e:
        logger.error(f"Error escalating to support: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to escalate to support"
        )

@sharepoint_router.get("/user-assistant/session/{session_id}")
async def get_user_session_history(
    session_id: str,
    current_user: UserInfo = Depends(require_permission(Permissions.USER_ASSISTANT_USE))
):
    """Get user session history"""
    
    try:
        history = await user_assistant.get_session_history(session_id)
        return history
        
    except Exception as e:
        logger.error(f"Error getting session history: {e}")
        raise HTTPException(
            status_code=404,
            detail="Session not found"
        )

@sharepoint_router.post("/user-assistant/feedback")
async def submit_user_feedback(
    feedback_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Submit user feedback on AI responses"""
    
    try:
        # Store feedback
        insert_query = text("""
            INSERT INTO user_feedback (
                session_id, rating, comment, helpful, 
                user_id, created_at
            ) VALUES (
                :session_id, :rating, :comment, :helpful,
                :user_id, CURRENT_TIMESTAMP
            )
        """)
        
        db.execute(insert_query, {
            "session_id": feedback_data.get("session_id"),
            "rating": feedback_data.get("rating"),
            "comment": feedback_data.get("comment", ""),
            "helpful": feedback_data.get("helpful", False),
            "user_id": current_user["id"]
        })
        
        db.commit()
        
        # Update Freshservice ticket if applicable
        if feedback_data.get("ticket_id") and feedback_data.get("rating", 0) <= 2:
            background_tasks.add_task(
                update_freshservice_with_feedback,
                feedback_data,
                db
            )
        
        return {"status": "feedback_recorded", "message": "Thank you for your feedback!"}
        
    except Exception as e:
        logger.error(f"Error submitting user feedback: {e}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail="Failed to record feedback"
        )

# Staff Assistant Routes
@sharepoint_router.post("/staff-assistant/analyze", response_model=TicketAnalysis)
async def analyze_ticket(
    request: TicketAnalysisRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: UserInfo = Depends(require_permission(Permissions.STAFF_ASSISTANT_USE))
):
    """Analyze ticket and provide AI assistance to staff"""
    
    try:
        analysis = await staff_assistant.analyze_ticket(request, db)
        
        # Log staff analysis for metrics
        background_tasks.add_task(log_staff_analysis, request, analysis, db)
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing ticket: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to analyze ticket"
        )

@sharepoint_router.post("/staff-assistant/feedback")
async def record_staff_feedback(
    feedback: StaffFeedback,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Record staff feedback on AI suggestions"""
    
    if "staff" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Staff privileges required"
        )
    
    try:
        result = await staff_assistant.record_staff_feedback(feedback, db)
        return result
        
    except Exception as e:
        logger.error(f"Error recording staff feedback: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to record feedback"
        )

@sharepoint_router.post("/staff-assistant/knowledge-update")
async def update_knowledge_base(
    update: KnowledgeUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Update knowledge base with staff insights"""
    
    if "staff" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Staff privileges required"
        )
    
    try:
        result = await staff_assistant.update_knowledge_base(update, db)
        
        # Trigger background reindexing
        background_tasks.add_task(reindex_knowledge_base, update)
        
        return result
        
    except Exception as e:
        logger.error(f"Error updating knowledge base: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to update knowledge base"
        )

@sharepoint_router.get("/staff-assistant/analytics/{staff_id}")
async def get_staff_analytics(
    staff_id: str,
    days_back: int = 30,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get staff AI assistance analytics"""
    
    # Verify staff can access analytics (either own data or admin)
    if staff_id != current_user["id"] and "admin" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Access denied"
        )
    
    try:
        analytics = await staff_assistant.get_staff_analytics(staff_id, days_back, db)
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting staff analytics: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve analytics"
        )

# Freshservice Integration Routes
@sharepoint_router.get("/freshservice/ticket/{ticket_id}")
async def get_freshservice_ticket(
    ticket_id: int,
    include_conversations: bool = False,
    current_user: dict = Depends(get_current_user)
):
    """Get ticket details from Freshservice"""
    
    if "staff" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Staff privileges required"
        )
    
    try:
        config = FreshserviceConfig()
        
        async with FreshserviceIntegration(config) as fs:
            include = ["conversations"] if include_conversations else []
            ticket = await fs.get_ticket(ticket_id, include=include)
            
        return ticket
        
    except Exception as e:
        logger.error(f"Error getting Freshservice ticket: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve ticket"
        )

@sharepoint_router.post("/freshservice/ticket/{ticket_id}/note")
async def add_note_to_ticket(
    ticket_id: int,
    note_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Add note to Freshservice ticket"""
    
    if "staff" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Staff privileges required"
        )
    
    try:
        config = FreshserviceConfig()
        
        async with FreshserviceIntegration(config) as fs:
            from .freshworks_integration import FreshserviceNote
            
            note = FreshserviceNote(
                body=note_data["body"],
                private=note_data.get("private", False),
                notify_emails=note_data.get("notify_emails", [])
            )
            
            result = await fs.add_note_to_ticket(ticket_id, note)
            
        return result
        
    except Exception as e:
        logger.error(f"Error adding note to ticket: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to add note"
        )

@sharepoint_router.get("/freshservice/sync-status")
async def get_sync_status(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get Freshservice sync status"""
    
    if "admin" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Admin privileges required"
        )
    
    try:
        # Get sync statistics
        query = text("""
            SELECT 
                COUNT(*) as total_tickets,
                COUNT(CASE WHEN external_ticket_id IS NOT NULL THEN 1 END) as synced_tickets,
                MAX(last_updated) as last_sync,
                COUNT(CASE WHEN status = 'sync_error' THEN 1 END) as sync_errors
            FROM test_tickets
            WHERE created_date > CURRENT_TIMESTAMP - INTERVAL '7 days'
        """)
        
        result = db.execute(query).fetchone()
        
        return {
            "total_tickets": result[0] if result[0] else 0,
            "synced_tickets": result[1] if result[1] else 0,
            "sync_percentage": round((result[1] / result[0] * 100) if result[0] > 0 else 0, 1),
            "last_sync": result[2].isoformat() if result[2] else None,
            "sync_errors": result[3] if result[3] else 0,
            "status": "healthy" if (result[3] or 0) < 5 else "warning"
        }
        
    except Exception as e:
        logger.error(f"Error getting sync status: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to get sync status"
        )

# Analytics Routes
@sharepoint_router.get("/analytics/dashboard")
async def get_dashboard_analytics(
    days_back: int = 30,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """Get comprehensive analytics dashboard data"""
    
    if "admin" not in current_user.get("roles", []) and "staff" not in current_user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Staff privileges required"
        )
    
    try:
        # User engagement metrics
        user_query = text("""
            SELECT 
                COUNT(*) as total_queries,
                COUNT(DISTINCT user_id) as unique_users,
                AVG(rating) as avg_rating,
                COUNT(CASE WHEN helpful = true THEN 1 END) as helpful_responses
            FROM user_feedback
            WHERE created_at > CURRENT_TIMESTAMP - INTERVAL :days DAY
        """)
        
        user_result = db.execute(user_query, {"days": days_back}).fetchone()
        
        # Staff analytics
        staff_query = text("""
            SELECT 
                COUNT(*) as total_analyses,
                COUNT(DISTINCT staff_id) as active_staff,
                AVG(rating) as avg_staff_rating,
                SUM(time_saved) as total_time_saved
            FROM staff_feedback
            WHERE created_at > CURRENT_TIMESTAMP - INTERVAL :days DAY
        """)
        
        staff_result = db.execute(staff_query, {"days": days_back}).fetchone()
        
        # Escalation metrics
        escalation_query = text("""
            SELECT 
                COUNT(*) as total_escalations,
                COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_escalations
            FROM test_tickets
            WHERE source = 'ai_escalation'
                AND created_date > CURRENT_TIMESTAMP - INTERVAL :days DAY
        """)
        
        escalation_result = db.execute(escalation_query, {"days": days_back}).fetchone()
        
        return {
            "period_days": days_back,
            "user_metrics": {
                "total_queries": user_result[0] if user_result[0] else 0,
                "unique_users": user_result[1] if user_result[1] else 0,
                "average_rating": round(user_result[2], 2) if user_result[2] else 0,
                "helpful_responses": user_result[3] if user_result[3] else 0,
                "satisfaction_rate": round((user_result[3] / user_result[0] * 100) if user_result[0] > 0 else 0, 1)
            },
            "staff_metrics": {
                "total_analyses": staff_result[0] if staff_result[0] else 0,
                "active_staff": staff_result[1] if staff_result[1] else 0,
                "average_rating": round(staff_result[2], 2) if staff_result[2] else 0,
                "total_time_saved": staff_result[3] if staff_result[3] else 0
            },
            "escalation_metrics": {
                "total_escalations": escalation_result[0] if escalation_result[0] else 0,
                "resolved_escalations": escalation_result[1] if escalation_result[1] else 0,
                "resolution_rate": round((escalation_result[1] / escalation_result[0] * 100) if escalation_result[0] > 0 else 0, 1)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting dashboard analytics: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve analytics"
        )

# Background Tasks
async def log_user_query(query: UserQuery, db: Session):
    """Log user query for analytics"""
    try:
        insert_query = text("""
            INSERT INTO user_query_log (
                user_id, user_email, query_text, 
                department, session_id, created_at
            ) VALUES (
                :user_id, :user_email, :query_text,
                :department, :session_id, CURRENT_TIMESTAMP
            )
        """)
        
        db.execute(insert_query, {
            "user_id": query.user_id,
            "user_email": query.user_email,
            "query_text": query.query,
            "department": query.department,
            "session_id": query.session_id
        })
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error logging user query: {e}")
        db.rollback()

async def log_ai_response(query: UserQuery, response: AIResponse, db: Session):
    """Log AI response for improvement"""
    try:
        insert_query = text("""
            INSERT INTO ai_response_log (
                session_id, query_text, response_text,
                confidence_score, requires_escalation, suggested_category,
                created_at
            ) VALUES (
                :session_id, :query_text, :response_text,
                :confidence_score, :requires_escalation, :suggested_category,
                CURRENT_TIMESTAMP
            )
        """)
        
        db.execute(insert_query, {
            "session_id": response.session_id,
            "query_text": query.query,
            "response_text": response.response,
            "confidence_score": response.confidence_score,
            "requires_escalation": response.requires_escalation,
            "suggested_category": response.suggested_category
        })
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error logging AI response: {e}")
        db.rollback()

async def log_escalation(escalation: EscalationRequest, result: Dict[str, Any], db: Session):
    """Log escalation event"""
    try:
        insert_query = text("""
            INSERT INTO escalation_log (
                session_id, ticket_id, user_feedback,
                escalation_reason, created_at
            ) VALUES (
                :session_id, :ticket_id, :user_feedback,
                :escalation_reason, CURRENT_TIMESTAMP
            )
        """)
        
        db.execute(insert_query, {
            "session_id": escalation.session_id,
            "ticket_id": result.get("ticket_id"),
            "user_feedback": escalation.user_feedback,
            "escalation_reason": "user_request"
        })
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error logging escalation: {e}")
        db.rollback()

async def log_staff_analysis(request: TicketAnalysisRequest, analysis: TicketAnalysis, db: Session):
    """Log staff analysis for metrics"""
    try:
        insert_query = text("""
            INSERT INTO staff_analysis_log (
                ticket_id, staff_id, analysis_depth,
                suggestions_count, confidence_score, created_at
            ) VALUES (
                :ticket_id, :staff_id, :analysis_depth,
                :suggestions_count, :confidence_score, CURRENT_TIMESTAMP
            )
        """)
        
        avg_confidence = sum(s.confidence_score for s in analysis.resolution_suggestions) / len(analysis.resolution_suggestions) if analysis.resolution_suggestions else 0
        
        db.execute(insert_query, {
            "ticket_id": request.ticket_id,
            "staff_id": request.staff_id,
            "analysis_depth": request.analysis_depth,
            "suggestions_count": len(analysis.resolution_suggestions),
            "confidence_score": avg_confidence
        })
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error logging staff analysis: {e}")
        db.rollback()

async def update_freshservice_with_feedback(feedback_data: Dict[str, Any], db: Session):
    """Update Freshservice ticket with negative feedback"""
    try:
        await update_ticket_from_chat_feedback(
            int(feedback_data["ticket_id"]),
            feedback_data,
            db
        )
        
    except Exception as e:
        logger.error(f"Error updating Freshservice with feedback: {e}")

async def reindex_knowledge_base(update: KnowledgeUpdate):
    """Trigger knowledge base reindexing"""
    try:
        # Placeholder for vector store reindexing
        logger.info(f"Knowledge base reindexing triggered for category: {update.category}")
        
    except Exception as e:
        logger.error(f"Error reindexing knowledge base: {e}")

# Health check
@sharepoint_router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "user_assistant": "operational",
            "staff_assistant": "operational",
            "freshservice_integration": "operational",
            "database": "operational"
        }
    }