#!/usr/bin/env python3
"""
Check what packages are available in the current environment
"""

import sys

def check_package(name, import_name=None):
    """Check if a package is available"""
    if import_name is None:
        import_name = name
    
    try:
        __import__(import_name)
        print(f"✅ {name} - Available")
        return True
    except ImportError:
        print(f"❌ {name} - Not available")
        return False

def main():
    print("🔍 Checking Available Packages")
    print("=" * 40)
    print(f"Python version: {sys.version_info[:2]}")
    print()
    
    # Check essential packages
    packages = [
        ('FastAPI', 'fastapi'),
        ('Uvicorn', 'uvicorn'),
        ('Streamlit', 'streamlit'),
        ('Pydantic', 'pydantic'),
        ('Requests', 'requests'),
        ('Pandas', 'pandas'),
        ('Plotly', 'plotly'),
        ('SQLAlchemy', 'sqlalchemy'),
        ('Psycopg2', 'psycopg2'),
        ('Redis', 'redis')
    ]
    
    available = 0
    total = len(packages)
    
    for name, import_name in packages:
        if check_package(name, import_name):
            available += 1
    
    print()
    print(f"📊 Summary: {available}/{total} packages available")
    
    if available >= 3:  # FastAPI, Uvicorn, Streamlit minimum
        print("🎉 Sufficient packages for basic operation!")
    else:
        print("⚠️  Limited packages available. Platform may run with reduced functionality.")
    
    # Check built-in packages that don't require installation
    print("\n🐍 Built-in Python capabilities:")
    builtin_packages = [
        ('HTTP Server', 'http.server'),
        ('JSON', 'json'),
        ('SQLite', 'sqlite3'),
        ('Threading', 'threading'),
        ('Subprocess', 'subprocess'),
        ('Socket', 'socket'),
        ('Pathlib', 'pathlib')
    ]
    
    for name, import_name in builtin_packages:
        check_package(name, import_name)

if __name__ == "__main__":
    main()
