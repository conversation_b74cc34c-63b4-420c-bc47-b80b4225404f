





import os
import sys
import subprocess
from pathlib import Path

# Check current directory
current_dir = Path.cwd()
print(f"Current directory: {current_dir}")

# Check Python version
print(f"Python version: {sys.version}")

# Check if we're in the project root
project_files = ['requirements.txt', 'pyproject.toml', 'backend', 'scripts']
missing_files = [f for f in project_files if not Path(f).exists()]

if missing_files:
    print(f"⚠️  Missing project files: {missing_files}")
    print("Please ensure you're running this notebook from the project root directory.")
else:
    print("✅ All project files found!")





# Install UV package manager first
!pip install uv

# Check if UV is installed
try:
    result = subprocess.run(['uv', '--version'], capture_output=True, text=True)
    print(f"✅ UV installed: {result.stdout.strip()}")
    use_uv = True
except FileNotFoundError:
    print("⚠️  UV not found, will use pip instead")
    use_uv = False


# Install dependencies
if use_uv:
    print("Installing dependencies with UV (faster)...")
    !uv pip install -r requirements.txt
else:
    print("Installing dependencies with pip...")
    !pip install -r requirements.txt

print("\n✅ Dependencies installation completed!")





# Load environment variables
from dotenv import load_dotenv
import os

# Load .env file
load_dotenv()

# Display current configuration (without sensitive values)
config_vars = {
    'CHATBOT_DB_HOST': os.getenv('CHATBOT_DB_HOST', 'localhost'),
    'CHATBOT_DB_PORT': os.getenv('CHATBOT_DB_PORT', '5432'),
    'CHATBOT_DB_NAME': os.getenv('CHATBOT_DB_NAME', 'support_chatbot'),
    'MILVUS_HOST': os.getenv('MILVUS_HOST', 'localhost'),
    'MILVUS_PORT': os.getenv('MILVUS_PORT', '19530'),
    'EMBEDDING_MODEL': os.getenv('EMBEDDING_MODEL', 'all-MiniLM-L6-v2'),
    'CHUNK_SIZE': os.getenv('CHUNK_SIZE', '1000'),
    'CHUNK_OVERLAP': os.getenv('CHUNK_OVERLAP', '200'),
}

print("📋 Current Configuration:")
for key, value in config_vars.items():
    print(f"  {key}: {value}")

print("\n✅ Configuration loaded!")





# Check if Docker is available
try:
    result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
    print(f"✅ Docker available: {result.stdout.strip()}")
    docker_available = True
except FileNotFoundError:
    print("⚠️  Docker not found. Please install Docker to continue.")
    docker_available = False

if docker_available:
    # Check if docker-compose is available
    try:
        result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
        print(f"✅ Docker Compose available: {result.stdout.strip()}")
        compose_available = True
    except FileNotFoundError:
        print("⚠️  Docker Compose not found.")
        compose_available = False


if docker_available and compose_available:
    print("🚀 Starting Docker services...")
    
    # Start infrastructure services
    result = subprocess.run(['docker-compose', 'up', '-d'], 
                          capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Docker services started successfully!")
        
        # Check service status
        result = subprocess.run(['docker-compose', 'ps'], 
                              capture_output=True, text=True)
        print("\n📊 Service Status:")
        print(result.stdout)
    else:
        print(f"❌ Error starting Docker services: {result.stderr}")
else:
    print("⚠️  Docker not available. You'll need to manually start PostgreSQL and Milvus.")





# Test database connection
import time
import psycopg2
from sqlalchemy import create_engine, text

# Wait for PostgreSQL to be ready
print("⏳ Waiting for PostgreSQL to be ready...")
time.sleep(10)

# Database connection parameters
db_config = {
    'host': os.getenv('CHATBOT_DB_HOST', 'localhost'),
    'port': os.getenv('CHATBOT_DB_PORT', '5432'),
    'database': os.getenv('CHATBOT_DB_NAME', 'support_chatbot'),
    'user': os.getenv('CHATBOT_DB_USER', 'chatbot_user'),
    'password': os.getenv('CHATBOT_DB_PASSWORD', 'chatbot_password')
}

# Create database URL
db_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"

try:
    # Test connection
    engine = create_engine(db_url)
    with engine.connect() as conn:
        result = conn.execute(text("SELECT 1"))
        print("✅ Database connection successful!")
        
except Exception as e:
    print(f"❌ Database connection failed: {e}")
    print("\n🔧 To fix this, run the following commands:")
    print("1. Connect to PostgreSQL as admin: psql -U postgres -h localhost")
    print("2. Create user and database:")
    print(f"   CREATE USER {db_config['user']} WITH PASSWORD '{db_config['password']}';")
    print(f"   CREATE DATABASE {db_config['database']} OWNER {db_config['user']};")
    print(f"   GRANT ALL PRIVILEGES ON DATABASE {db_config['database']} TO {db_config['user']};")


# Initialize database schema
schema_file = Path('schema_chatbot.sql')

if schema_file.exists():
    print("📋 Initializing database schema...")
    
    try:
        with open(schema_file, 'r') as f:
            schema_sql = f.read()
        
        # Execute schema
        with engine.connect() as conn:
            # Split and execute each statement
            statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
            
            for stmt in statements:
                try:
                    conn.execute(text(stmt))
                except Exception as e:
                    if "already exists" not in str(e):
                        print(f"Warning: {e}")
            
            conn.commit()
        
        print("✅ Database schema initialized successfully!")
        
        # Verify tables
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            tables = [row[0] for row in result]
            print(f"📊 Created tables: {', '.join(tables)}")
            
    except Exception as e:
        print(f"❌ Error initializing schema: {e}")
else:
    print("⚠️  Schema file not found. Please ensure schema_chatbot.sql exists.")





# Test Milvus connection
try:
    from pymilvus import connections, utility
    
    print("⏳ Connecting to Milvus...")
    
    milvus_host = os.getenv('MILVUS_HOST', 'localhost')
    milvus_port = os.getenv('MILVUS_PORT', '19530')
    
    # Connect to Milvus
    connections.connect("default", host=milvus_host, port=milvus_port)
    
    # Check connection
    print(f"✅ Connected to Milvus at {milvus_host}:{milvus_port}")
    
    # List existing collections
    collections = utility.list_collections()
    print(f"📊 Existing collections: {collections}")
    
except Exception as e:
    print(f"❌ Milvus connection failed: {e}")
    print("\n🔧 Make sure Milvus is running:")
    print("docker-compose up -d milvus-standalone")





# Run the setup test script
print("🧪 Running comprehensive setup tests...")

# Add project root to Python path
import sys
from pathlib import Path
sys.path.append(str(Path.cwd()))

# Import and run tests
try:
    from scripts.test_setup import main as run_tests
    
    # Run tests
    success = run_tests()
    
    if success:
        print("\n🎉 All tests passed! Setup is complete.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        
except Exception as e:
    print(f"❌ Error running tests: {e}")



