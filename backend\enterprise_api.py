"""
Enterprise Technical Support API
FastAPI application with advanced ticket management, AI integration, and comprehensive features
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, Query, Path, Body, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from enterprise_ticket_manager import (
    EnterpriseTicketManager,
    TicketCreateRequest,
    TicketUpdateRequest,
    TicketResponse,
    ActivityCreateRequest,
    AutomationRuleRequest,
    TicketPriority,
    TicketStatus,
    TicketSource,
    UserRole
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# =============================================================================
# APPLICATION SETUP
# =============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    logger.info("Starting Enterprise Technical Support API")

    # Initialize any startup tasks here
    yield

    logger.info("Shutting down Enterprise Technical Support API")

app = FastAPI(
    title="Enterprise Technical Support API",
    description="Advanced AI-powered technical support platform with comprehensive ticket management",
    version="2.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =============================================================================
# DEPENDENCY INJECTION
# =============================================================================

def get_database_url() -> str:
    """Get database URL from environment"""
    return os.getenv(
        "DATABASE_URL",
        "postgresql://user:password@localhost:5432/enterprise_support"
    )

def get_organization_id() -> str:
    """Get organization ID from environment or request context"""
    # In production, this would be extracted from JWT token or request headers
    return os.getenv("ORGANIZATION_ID", "00000000-0000-0000-0000-000000000001")

def get_ticket_manager(
    database_url: str = Depends(get_database_url),
    organization_id: str = Depends(get_organization_id)
) -> EnterpriseTicketManager:
    """Get ticket manager instance"""
    return EnterpriseTicketManager(database_url, organization_id)

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """Get current user from JWT token"""
    # In production, implement proper JWT validation
    # For now, return a mock user
    return {
        "id": "00000000-0000-0000-0000-000000000021",
        "email": "<EMAIL>",
        "name": "System Administrator",
        "role": "admin",
        "organization_id": get_organization_id()
    }

# =============================================================================
# RESPONSE MODELS
# =============================================================================

class APIResponse(BaseModel):
    """Standard API response wrapper"""
    success: bool = True
    message: str = "Operation completed successfully"
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class PaginatedResponse(BaseModel):
    """Paginated response wrapper"""
    success: bool = True
    data: List[Any]
    total_count: int
    page_size: int
    page_number: int
    total_pages: int
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class TicketSearchRequest(BaseModel):
    """Request model for ticket search"""
    query: Optional[str] = None
    status: Optional[List[str]] = None
    priority: Optional[List[str]] = None
    category: Optional[List[str]] = None
    assigned_to: Optional[str] = None
    requester: Optional[str] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    sla_breached: Optional[bool] = None
    ai_generated: Optional[bool] = None
    tags: Optional[List[str]] = None

# =============================================================================
# TICKET MANAGEMENT ENDPOINTS
# =============================================================================

@app.post("/api/v2/tickets", response_model=APIResponse, status_code=status.HTTP_201_CREATED)
async def create_ticket(
    ticket_data: TicketCreateRequest,
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Create a new support ticket with enterprise features

    Features:
    - Automatic ticket number generation
    - SLA calculation and tracking
    - AI-powered categorization
    - Automation rule execution
    - Comprehensive audit trail
    """
    try:
        ticket = await ticket_manager.create_ticket(
            ticket_data=ticket_data,
            created_by_id=current_user["id"]
        )

        return APIResponse(
            message="Ticket created successfully",
            data=ticket
        )

    except Exception as e:
        logger.error(f"Error creating ticket: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create ticket: {str(e)}"
        )

@app.get("/api/v2/tickets/{ticket_id}", response_model=APIResponse)
async def get_ticket(
    ticket_id: str = Path(..., description="Ticket ID"),
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get ticket details by ID"""
    try:
        ticket = await ticket_manager.get_ticket(ticket_id)

        if not ticket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Ticket not found"
            )

        return APIResponse(
            message="Ticket retrieved successfully",
            data=ticket
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving ticket {ticket_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve ticket: {str(e)}"
        )

@app.put("/api/v2/tickets/{ticket_id}", response_model=APIResponse)
async def update_ticket(
    ticket_id: str = Path(..., description="Ticket ID"),
    update_data: TicketUpdateRequest = Body(...),
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update an existing ticket with comprehensive change tracking

    Features:
    - Field-level change tracking
    - Automatic activity creation
    - SLA recalculation
    - Automation rule execution
    - Version control
    """
    try:
        updated_ticket = await ticket_manager.update_ticket(
            ticket_id=ticket_id,
            update_data=update_data,
            updated_by_id=current_user["id"],
            actor_name=current_user["name"],
            actor_email=current_user["email"]
        )

        return APIResponse(
            message="Ticket updated successfully",
            data=updated_ticket
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating ticket {ticket_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update ticket: {str(e)}"
        )

@app.post("/api/v2/tickets/search", response_model=PaginatedResponse)
async def search_tickets(
    search_request: TicketSearchRequest = Body(...),
    limit: int = Query(50, ge=1, le=100, description="Number of results per page"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: str = Query("DESC", regex="^(ASC|DESC)$", description="Sort order"),
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Advanced ticket search with multiple filters and full-text search

    Features:
    - Full-text search across subject and description
    - Multiple filter criteria
    - Pagination support
    - Flexible sorting
    - SLA breach detection
    - AI-generated ticket filtering
    """
    try:
        search_result = await ticket_manager.search_tickets(
            query=search_request.query,
            status=search_request.status,
            priority=search_request.priority,
            category=search_request.category,
            assigned_to=search_request.assigned_to,
            requester=search_request.requester,
            created_after=search_request.created_after,
            created_before=search_request.created_before,
            sla_breached=search_request.sla_breached,
            ai_generated=search_request.ai_generated,
            tags=search_request.tags,
            limit=limit,
            offset=offset,
            sort_by=sort_by,
            sort_order=sort_order
        )

        return PaginatedResponse(
            data=search_result["tickets"],
            total_count=search_result["total_count"],
            page_size=search_result["page_size"],
            page_number=search_result["page_number"],
            total_pages=search_result["total_pages"]
        )
# =============================================================================
# AUTOMATION AND WORKFLOW ENDPOINTS
# =============================================================================

@app.post("/api/v2/automation/rules", response_model=APIResponse)
async def create_automation_rule(
    rule_data: AutomationRuleRequest = Body(...),
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new automation rule"""
    try:
        # Only admins and supervisors can create automation rules
        if current_user["role"] not in ["admin", "supervisor"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to create automation rules"
            )

        # Implementation would go here - create automation rule in database
        # For now, return success response

        return APIResponse(
            message="Automation rule created successfully",
            data={"rule_id": "new-rule-id", "name": rule_data.name}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating automation rule: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create automation rule: {str(e)}"
        )

@app.get("/api/v2/automation/rules", response_model=APIResponse)
async def list_automation_rules(
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    rule_type: Optional[str] = Query(None, description="Filter by rule type"),
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """List automation rules"""
    try:
        # Implementation would go here - fetch automation rules from database
        # For now, return mock data

        rules = [
            {
                "id": "rule-1",
                "name": "Auto-assign Password Resets",
                "rule_type": "auto_assign",
                "is_active": True,
                "execution_count": 45,
                "success_rate": 98.5
            },
            {
                "id": "rule-2",
                "name": "Escalate Critical Issues",
                "rule_type": "auto_escalate",
                "is_active": True,
                "execution_count": 12,
                "success_rate": 100.0
            }
        ]

        return APIResponse(
            message="Automation rules retrieved successfully",
            data=rules
        )

    except Exception as e:
        logger.error(f"Error listing automation rules: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list automation rules: {str(e)}"
        )

# =============================================================================
# ANALYTICS AND REPORTING ENDPOINTS
# =============================================================================

@app.get("/api/v2/analytics/dashboard", response_model=APIResponse)
async def get_dashboard_analytics(
    date_from: Optional[datetime] = Query(None, description="Start date for analytics"),
    date_to: Optional[datetime] = Query(None, description="End date for analytics"),
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get dashboard analytics and KPIs"""
    try:
        # Set default date range if not provided
        if not date_to:
            date_to = datetime.utcnow()
        if not date_from:
            date_from = date_to - timedelta(days=30)

        # Implementation would fetch real analytics from database
        # For now, return mock analytics data

        analytics = {
            "summary": {
                "total_tickets": 1247,
                "open_tickets": 89,
                "resolved_tickets": 1158,
                "avg_resolution_time_hours": 4.2,
                "sla_compliance_rate": 94.5,
                "customer_satisfaction": 4.3
            },
            "trends": {
                "tickets_created_today": 23,
                "tickets_resolved_today": 31,
                "ai_resolution_rate": 67.8,
                "automation_success_rate": 96.2
            },
            "sla_status": {
                "response_breaches": 3,
                "resolution_breaches": 1,
                "at_risk_tickets": 7
            },
            "agent_performance": [
                {
                    "agent_name": "John Smith",
                    "active_tickets": 12,
                    "avg_resolution_time": 3.8,
                    "satisfaction_rating": 4.5
                },
                {
                    "agent_name": "Jane Doe",
                    "active_tickets": 8,
                    "avg_resolution_time": 2.9,
                    "satisfaction_rating": 4.7
                }
            ],
            "category_breakdown": [
                {"category": "Password & Access", "count": 45, "percentage": 36.2},
                {"category": "Email & Communication", "count": 32, "percentage": 25.8},
                {"category": "Software & Applications", "count": 28, "percentage": 22.6},
                {"category": "Hardware & Equipment", "count": 19, "percentage": 15.4}
            ]
        }

        return APIResponse(
            message="Dashboard analytics retrieved successfully",
            data=analytics
        )

    except Exception as e:
        logger.error(f"Error retrieving dashboard analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve analytics: {str(e)}"
        )

@app.get("/api/v2/analytics/performance", response_model=APIResponse)
async def get_performance_metrics(
    metric_type: str = Query(..., description="Type of performance metric"),
    period: str = Query("7d", description="Time period (1d, 7d, 30d, 90d)"),
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get detailed performance metrics"""
    try:
        # Implementation would calculate real metrics from database
        # For now, return mock performance data

        if metric_type == "resolution_time":
            data = {
                "metric": "Average Resolution Time",
                "period": period,
                "current_value": 4.2,
                "previous_value": 5.1,
                "improvement": 17.6,
                "trend": "improving",
                "daily_values": [5.2, 4.8, 4.5, 4.1, 3.9, 4.2, 4.0]
            }
        elif metric_type == "sla_compliance":
            data = {
                "metric": "SLA Compliance Rate",
                "period": period,
                "current_value": 94.5,
                "previous_value": 91.2,
                "improvement": 3.3,
                "trend": "improving",
                "daily_values": [91.2, 92.1, 93.5, 94.2, 94.8, 94.5, 95.1]
            }
        else:
            data = {
                "metric": metric_type,
                "period": period,
                "message": "Metric type not implemented yet"
            }

        return APIResponse(
            message="Performance metrics retrieved successfully",
            data=data
        )

    except Exception as e:
        logger.error(f"Error retrieving performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve performance metrics: {str(e)}"
        )

# =============================================================================
# SYSTEM CONFIGURATION ENDPOINTS
# =============================================================================

@app.get("/api/v2/config/categories", response_model=APIResponse)
async def get_service_categories(
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get service categories and subcategories"""
    try:
        # Implementation would fetch from database
        # For now, return mock data

        categories = [
            {
                "id": "cat-1",
                "name": "Password & Access",
                "code": "PASSWORD",
                "description": "Password resets, account lockouts, access requests",
                "subcategories": [
                    {"id": "sub-1", "name": "Password Reset", "code": "PWD_RESET"},
                    {"id": "sub-2", "name": "Account Unlock", "code": "ACC_UNLOCK"}
                ]
            },
            {
                "id": "cat-2",
                "name": "Email & Communication",
                "code": "EMAIL",
                "description": "Email issues, Outlook problems, communication tools",
                "subcategories": [
                    {"id": "sub-3", "name": "Outlook Issues", "code": "OUTLOOK"},
                    {"id": "sub-4", "name": "Email Setup", "code": "EMAIL_SETUP"}
                ]
            }
        ]

        return APIResponse(
            message="Service categories retrieved successfully",
            data=categories
        )

    except Exception as e:
        logger.error(f"Error retrieving service categories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve service categories: {str(e)}"
        )

@app.get("/api/v2/config/priorities", response_model=APIResponse)
async def get_ticket_priorities(
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get ticket priorities"""
    try:
        priorities = [
            {"id": "pri-1", "name": "Low", "code": "LOW", "level": 1, "sla_response": "4 hours"},
            {"id": "pri-2", "name": "Medium", "code": "MEDIUM", "level": 2, "sla_response": "2 hours"},
            {"id": "pri-3", "name": "High", "code": "HIGH", "level": 3, "sla_response": "1 hour"},
            {"id": "pri-4", "name": "Critical", "code": "CRITICAL", "level": 4, "sla_response": "30 minutes"},
            {"id": "pri-5", "name": "Emergency", "code": "EMERGENCY", "level": 5, "sla_response": "15 minutes"}
        ]

        return APIResponse(
            message="Ticket priorities retrieved successfully",
            data=priorities
        )

    except Exception as e:
        logger.error(f"Error retrieving ticket priorities: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve ticket priorities: {str(e)}"
        )

# =============================================================================
# HEALTH CHECK AND STATUS ENDPOINTS
# =============================================================================

@app.get("/health", response_model=APIResponse)
async def health_check():
    """Health check endpoint"""
    return APIResponse(
        message="Enterprise Technical Support API is healthy",
        data={
            "status": "healthy",
            "version": "2.0.0",
            "timestamp": datetime.utcnow()
        }
    )

@app.get("/api/v2/status", response_model=APIResponse)
async def get_system_status(
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager)
):
    """Get system status and statistics"""
    try:
        # Implementation would check database connectivity and system health
        # For now, return mock status

        status_data = {
            "database": "connected",
            "ai_service": "operational",
            "automation_engine": "running",
            "active_sessions": 47,
            "queue_size": 3,
            "last_backup": "2025-07-06T10:30:00Z",
            "uptime": "7 days, 14 hours, 23 minutes"
        }

        return APIResponse(
            message="System status retrieved successfully",
            data=status_data
        )

    except Exception as e:
        logger.error(f"Error retrieving system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve system status: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "enterprise_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search tickets: {str(e)}"
        )

# =============================================================================
# TICKET ACTIVITIES ENDPOINTS
# =============================================================================

@app.post("/api/v2/tickets/{ticket_id}/activities", response_model=APIResponse)
async def create_ticket_activity(
    ticket_id: str = Path(..., description="Ticket ID"),
    activity_data: ActivityCreateRequest = Body(...),
    ticket_manager: EnterpriseTicketManager = Depends(get_ticket_manager),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new activity for a ticket"""
    try:
        # Verify ticket exists
        ticket = await ticket_manager.get_ticket(ticket_id)
        if not ticket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Ticket not found"
            )

        activity_id = await ticket_manager._create_activity(
            ticket_id=ticket_id,
            activity_type=activity_data.activity_type,
            actor_name=current_user["name"],
            actor_email=current_user["email"],
            actor_type="agent" if current_user["role"] in ["agent", "supervisor"] else "user",
            title=activity_data.title,
            content=activity_data.content,
            content_type=activity_data.content_type,
            visibility=activity_data.visibility,
            is_customer_visible=activity_data.is_customer_visible,
            time_spent=activity_data.time_spent,
            billable_time=activity_data.billable_time,
            work_type=activity_data.work_type,
            metadata=activity_data.metadata
        )

        return APIResponse(
            message="Activity created successfully",
            data={"activity_id": activity_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating activity for ticket {ticket_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create activity: {str(e)}"
        )
