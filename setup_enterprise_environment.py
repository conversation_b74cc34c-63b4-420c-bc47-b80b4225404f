#!/usr/bin/env python3
"""
Enterprise Platform Environment Setup Script
Sets up the complete environment for the enterprise technical support platform
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnvironmentSetup:
    """Handles environment setup for the enterprise platform"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.venv_dir = self.base_dir / 'venv'
        
    def check_system_requirements(self) -> bool:
        """Check system requirements"""
        logger.info("🔍 Checking system requirements...")
        
        # Check Python version
        if sys.version_info < (3, 9):
            logger.error("❌ Python 3.9+ required. Current version: %s", sys.version)
            return False
        logger.info("✅ Python version: %s", sys.version.split()[0])
        
        # Check pip
        try:
            subprocess.run([sys.executable, '-m', 'pip', '--version'], check=True, capture_output=True)
            logger.info("✅ pip is available")
        except subprocess.CalledProcessError:
            logger.error("❌ pip is not available")
            return False
        
        return True
    
    def setup_virtual_environment(self) -> bool:
        """Set up Python virtual environment"""
        logger.info("🐍 Setting up virtual environment...")
        
        if self.venv_dir.exists():
            logger.info("📁 Virtual environment already exists")
            return True
        
        try:
            subprocess.run([sys.executable, '-m', 'venv', str(self.venv_dir)], check=True)
            logger.info("✅ Virtual environment created")
            return True
        except subprocess.CalledProcessError as e:
            logger.error("❌ Failed to create virtual environment: %s", e)
            return False
    
    def get_venv_python(self) -> str:
        """Get the virtual environment Python executable"""
        if os.name == 'nt':  # Windows
            return str(self.venv_dir / 'Scripts' / 'python.exe')
        else:  # Unix/Linux/macOS
            return str(self.venv_dir / 'bin' / 'python')
    
    def install_dependencies(self) -> bool:
        """Install Python dependencies"""
        logger.info("📦 Installing dependencies...")
        
        venv_python = self.get_venv_python()
        
        # Upgrade pip first
        try:
            subprocess.run([venv_python, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)
            logger.info("✅ pip upgraded")
        except subprocess.CalledProcessError as e:
            logger.error("❌ Failed to upgrade pip: %s", e)
            return False
        
        # Install requirements
        requirements_files = [
            'requirements-enterprise.txt',
            'requirements.txt',
            'requirements_essential.txt'
        ]
        
        for req_file in requirements_files:
            req_path = self.base_dir / req_file
            if req_path.exists():
                logger.info(f"📋 Installing from {req_file}...")
                try:
                    subprocess.run([
                        venv_python, '-m', 'pip', 'install', '-r', str(req_path)
                    ], check=True)
                    logger.info(f"✅ Installed dependencies from {req_file}")
                    break
                except subprocess.CalledProcessError as e:
                    logger.warning(f"⚠️  Failed to install from {req_file}: %s", e)
                    continue
        else:
            logger.error("❌ No valid requirements file found")
            return False
        
        return True
    
    def create_env_file(self) -> bool:
        """Create .env file with default configuration"""
        logger.info("⚙️  Creating environment configuration...")
        
        env_file = self.base_dir / '.env'
        
        if env_file.exists():
            logger.info("📄 .env file already exists")
            return True
        
        env_content = """# Enterprise Technical Support Platform Configuration
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/enterprise_support
ORGANIZATION_ID=00000000-0000-0000-0000-000000000001

# Security Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-here-minimum-32-chars-change-this
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Dashboard Configuration
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=8501

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# AI Configuration (Optional - add your OpenAI key)
# OPENAI_API_KEY=your-openai-api-key-here
AI_MODEL=gpt-4
AI_TEMPERATURE=0.7

# Email Configuration (Optional)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Feature Flags
ENABLE_AI_AUTOMATION=true
ENABLE_SLA_MONITORING=true
ENABLE_ANALYTICS=true
"""
        
        try:
            with open(env_file, 'w') as f:
                f.write(env_content)
            logger.info("✅ .env file created")
            return True
        except Exception as e:
            logger.error("❌ Failed to create .env file: %s", e)
            return False
    
    def check_database_tools(self) -> bool:
        """Check if database tools are available"""
        logger.info("🗄️  Checking database tools...")
        
        # Check PostgreSQL
        try:
            result = subprocess.run(['psql', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ PostgreSQL client available")
                return True
        except FileNotFoundError:
            pass
        
        logger.warning("⚠️  PostgreSQL client not found in PATH")
        logger.info("💡 Please install PostgreSQL or ensure it's in your PATH")
        logger.info("   - Windows: Download from https://www.postgresql.org/download/windows/")
        logger.info("   - macOS: brew install postgresql")
        logger.info("   - Ubuntu: sudo apt-get install postgresql-client")
        
        return False
    
    def check_redis_tools(self) -> bool:
        """Check if Redis tools are available"""
        logger.info("🔴 Checking Redis tools...")
        
        try:
            result = subprocess.run(['redis-server', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ Redis server available")
                return True
        except FileNotFoundError:
            pass
        
        logger.warning("⚠️  Redis server not found in PATH")
        logger.info("💡 Please install Redis or ensure it's in your PATH")
        logger.info("   - Windows: Download from https://github.com/microsoftarchive/redis/releases")
        logger.info("   - macOS: brew install redis")
        logger.info("   - Ubuntu: sudo apt-get install redis-server")
        
        return False
    
    def create_startup_scripts(self) -> bool:
        """Create platform-specific startup scripts"""
        logger.info("📝 Creating startup scripts...")
        
        # Windows batch file
        if os.name == 'nt':
            batch_content = """@echo off
echo Starting Enterprise Technical Support Platform...
call venv\\Scripts\\activate.bat
python start_enterprise_platform.py
pause
"""
            try:
                with open(self.base_dir / 'start_platform.bat', 'w') as f:
                    f.write(batch_content)
                logger.info("✅ Windows startup script created: start_platform.bat")
            except Exception as e:
                logger.error("❌ Failed to create Windows startup script: %s", e)
        
        # Unix shell script
        shell_content = """#!/bin/bash
echo "Starting Enterprise Technical Support Platform..."
source venv/bin/activate
python start_enterprise_platform.py
"""
        try:
            script_path = self.base_dir / 'start_platform.sh'
            with open(script_path, 'w') as f:
                f.write(shell_content)
            os.chmod(script_path, 0o755)  # Make executable
            logger.info("✅ Unix startup script created: start_platform.sh")
        except Exception as e:
            logger.error("❌ Failed to create Unix startup script: %s", e)
        
        return True
    
    def print_next_steps(self):
        """Print next steps for the user"""
        print("\n" + "="*60)
        print("🎉 ENVIRONMENT SETUP COMPLETE!")
        print("="*60)
        
        print("\n📋 Next Steps:")
        print("1. 🗄️  Set up PostgreSQL database:")
        print("   createdb enterprise_support")
        print("   psql enterprise_support < schema.sql")
        
        print("\n2. ⚙️  Edit .env file with your configuration:")
        print("   - Update DATABASE_URL with your PostgreSQL credentials")
        print("   - Add your OpenAI API key (optional)")
        print("   - Configure email settings (optional)")
        
        print("\n3. 🚀 Start the platform:")
        if os.name == 'nt':
            print("   start_platform.bat")
        else:
            print("   ./start_platform.sh")
        print("   OR")
        print("   python start_enterprise_platform.py")
        
        print("\n4. 🌐 Access the services:")
        print("   - API Server: http://localhost:8000")
        print("   - API Docs: http://localhost:8000/docs")
        print("   - Dashboard: http://localhost:8501")
        
        print("\n💡 Troubleshooting:")
        print("   - Check logs: enterprise_startup.log")
        print("   - Ensure PostgreSQL and Redis are running")
        print("   - Verify .env configuration")
        print("="*60)
    
    def run_setup(self) -> bool:
        """Run the complete setup process"""
        logger.info("🚀 Starting Enterprise Platform Environment Setup")
        
        steps = [
            ("System Requirements", self.check_system_requirements),
            ("Virtual Environment", self.setup_virtual_environment),
            ("Dependencies", self.install_dependencies),
            ("Environment File", self.create_env_file),
            ("Database Tools", self.check_database_tools),
            ("Redis Tools", self.check_redis_tools),
            ("Startup Scripts", self.create_startup_scripts)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"📍 Step: {step_name}")
            if not step_func():
                logger.error(f"❌ Setup failed at step: {step_name}")
                return False
        
        self.print_next_steps()
        return True

def main():
    """Main function"""
    setup = EnvironmentSetup()
    
    if setup.run_setup():
        logger.info("✅ Environment setup completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Environment setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
