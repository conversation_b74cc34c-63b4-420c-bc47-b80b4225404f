"""
Real-time ticket management system for technical support chatbot.
Integrates with main database and provides real-time updates.
"""

import asyncio
import json
import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
from sqlalchemy import create_engine, Column, String, DateTime, Text, Integer, Float, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.dialects.postgresql import UUID
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

Base = declarative_base()

class TicketStatus(Enum):
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    WAITING_FOR_USER = "waiting_for_user"
    RESOLVED = "resolved"
    CLOSED = "closed"
    ESCALATED = "escalated"

class TicketPriority(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"

class ChatRole(Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    AGENT = "agent"

# SQLAlchemy Models
class TicketORM(Base):
    __tablename__ = "tickets"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    status = Column(String(50), nullable=False, default=TicketStatus.OPEN.value)
    priority = Column(String(50), nullable=False, default=TicketPriority.MEDIUM.value)
    category = Column(String(100), nullable=False)
    
    # User information
    user_id = Column(String(100), nullable=False)
    user_name = Column(String(255), nullable=False)
    user_email = Column(String(255))
    user_department = Column(String(100), nullable=False)
    user_role = Column(String(50))
    
    # Assignment and resolution
    assigned_to = Column(String(255))
    assigned_at = Column(DateTime)
    resolved_by = Column(String(255))
    resolved_at = Column(DateTime)
    resolution = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Chat integration
    chat_session_id = Column(UUID(as_uuid=True))
    
    # Metadata
    tags = Column(Text)  # JSON string
    custom_fields = Column(Text)  # JSON string
    
    # Relationships
    chat_sessions = relationship("ChatSessionORM", back_populates="ticket")
    chat_messages = relationship("ChatMessageORM", back_populates="ticket")

class ChatSessionORM(Base):
    __tablename__ = "chat_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String(100), nullable=False)
    user_name = Column(String(255), nullable=False)
    user_department = Column(String(100), nullable=False)
    
    # Session info
    status = Column(String(50), default="active")
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    ended_at = Column(DateTime)
    
    # Ticket relationship
    ticket_id = Column(UUID(as_uuid=True), ForeignKey('tickets.id'))
    ticket = relationship("TicketORM", back_populates="chat_sessions")
    
    # Messages relationship
    messages = relationship("ChatMessageORM", back_populates="session")

class ChatMessageORM(Base):
    __tablename__ = "chat_messages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey('chat_sessions.id'), nullable=False)
    ticket_id = Column(UUID(as_uuid=True), ForeignKey('tickets.id'))
    
    role = Column(String(50), nullable=False)
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # AI metadata
    confidence_score = Column(Float)
    response_time_ms = Column(Integer)
    sources = Column(Text)  # JSON string
    metadata = Column(Text)  # JSON string
    
    # Relationships
    session = relationship("ChatSessionORM", back_populates="messages")
    ticket = relationship("TicketORM", back_populates="chat_messages")

# Data Classes for API
@dataclass
class TicketCreate:
    title: str
    description: str
    category: str
    user_id: str
    user_name: str
    user_department: str
    priority: str = TicketPriority.MEDIUM.value
    user_email: Optional[str] = None
    user_role: Optional[str] = None
    chat_session_id: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None

@dataclass
class TicketUpdate:
    status: Optional[str] = None
    priority: Optional[str] = None
    assigned_to: Optional[str] = None
    resolution: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None

@dataclass
class ChatMessageCreate:
    session_id: str
    role: str
    content: str
    ticket_id: Optional[str] = None
    confidence_score: Optional[float] = None
    response_time_ms: Optional[int] = None
    sources: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class RealTimeTicketManager:
    """
    Real-time ticket management system with database integration
    """
    
    def __init__(self, database_url: Optional[str] = None):
        """Initialize the ticket manager with database connection"""
        if database_url is None:
            database_url = os.getenv('DATABASE_URL', 'postgresql://chatbot_user:chatbot_password@localhost:5432/support_chatbot')
        
        self.engine = create_engine(database_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create tables if they don't exist
        Base.metadata.create_all(bind=self.engine)
        
        # Active connections for real-time updates
        self.active_connections: Dict[str, Any] = {}
        
        logger.info("Real-time ticket manager initialized")
    
    def get_db(self) -> Session:
        """Get database session"""
        db = self.SessionLocal()
        try:
            return db
        finally:
            pass  # Don't close here, let the caller handle it
    
    async def create_ticket(self, ticket_data: TicketCreate) -> Dict[str, Any]:
        """Create a new ticket"""
        db = self.get_db()
        try:
            # Create ticket
            ticket = TicketORM(
                title=ticket_data.title,
                description=ticket_data.description,
                category=ticket_data.category,
                priority=ticket_data.priority,
                user_id=ticket_data.user_id,
                user_name=ticket_data.user_name,
                user_email=ticket_data.user_email,
                user_department=ticket_data.user_department,
                user_role=ticket_data.user_role,
                chat_session_id=ticket_data.chat_session_id,
                tags=json.dumps(ticket_data.tags) if ticket_data.tags else None,
                custom_fields=json.dumps(ticket_data.custom_fields) if ticket_data.custom_fields else None
            )
            
            db.add(ticket)
            db.commit()
            db.refresh(ticket)
            
            ticket_dict = {
                'id': str(ticket.id),
                'title': ticket.title,
                'description': ticket.description,
                'status': ticket.status,
                'priority': ticket.priority,
                'category': ticket.category,
                'user_id': ticket.user_id,
                'user_name': ticket.user_name,
                'user_department': ticket.user_department,
                'created_at': ticket.created_at.isoformat(),
                'updated_at': ticket.updated_at.isoformat()
            }
            
            # Broadcast ticket creation to active connections
            await self._broadcast_update("ticket_created", ticket_dict)
            
            logger.info(f"Created ticket {ticket.id} for user {ticket.user_name}")
            return ticket_dict
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating ticket: {e}")
            raise
        finally:
            db.close()
    
    async def update_ticket(self, ticket_id: str, update_data: TicketUpdate) -> Dict[str, Any]:
        """Update an existing ticket"""
        db = self.get_db()
        try:
            ticket = db.query(TicketORM).filter(TicketORM.id == ticket_id).first()
            if not ticket:
                raise ValueError(f"Ticket {ticket_id} not found")
            
            # Update fields
            if update_data.status:
                ticket.status = update_data.status
                if update_data.status == TicketStatus.RESOLVED.value:
                    ticket.resolved_at = datetime.utcnow()
                    ticket.resolved_by = update_data.assigned_to or "system"
            
            if update_data.priority:
                ticket.priority = update_data.priority
            
            if update_data.assigned_to:
                ticket.assigned_to = update_data.assigned_to
                ticket.assigned_at = datetime.utcnow()
            
            if update_data.resolution:
                ticket.resolution = update_data.resolution
            
            if update_data.tags:
                ticket.tags = json.dumps(update_data.tags)
            
            if update_data.custom_fields:
                ticket.custom_fields = json.dumps(update_data.custom_fields)
            
            ticket.updated_at = datetime.utcnow()
            
            db.commit()
            db.refresh(ticket)
            
            ticket_dict = {
                'id': str(ticket.id),
                'title': ticket.title,
                'status': ticket.status,
                'priority': ticket.priority,
                'assigned_to': ticket.assigned_to,
                'resolution': ticket.resolution,
                'updated_at': ticket.updated_at.isoformat()
            }
            
            # Broadcast ticket update to active connections
            await self._broadcast_update("ticket_updated", ticket_dict)
            
            logger.info(f"Updated ticket {ticket.id}")
            return ticket_dict
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating ticket {ticket_id}: {e}")
            raise
        finally:
            db.close()
    
    def get_tickets(self, 
                   user_id: Optional[str] = None, 
                   status: Optional[str] = None,
                   priority: Optional[str] = None,
                   category: Optional[str] = None,
                   limit: int = 100) -> List[Dict[str, Any]]:
        """Get tickets with optional filters"""
        db = self.get_db()
        try:
            query = db.query(TicketORM)
            
            if user_id:
                query = query.filter(TicketORM.user_id == user_id)
            if status:
                query = query.filter(TicketORM.status == status)
            if priority:
                query = query.filter(TicketORM.priority == priority)
            if category:
                query = query.filter(TicketORM.category == category)
            
            tickets = query.order_by(TicketORM.created_at.desc()).limit(limit).all()
            
            return [
                {
                    'id': str(ticket.id),
                    'title': ticket.title,
                    'description': ticket.description,
                    'status': ticket.status,
                    'priority': ticket.priority,
                    'category': ticket.category,
                    'user_id': ticket.user_id,
                    'user_name': ticket.user_name,
                    'user_department': ticket.user_department,
                    'assigned_to': ticket.assigned_to,
                    'resolution': ticket.resolution,
                    'created_at': ticket.created_at.isoformat(),
                    'updated_at': ticket.updated_at.isoformat(),
                    'chat_session_id': str(ticket.chat_session_id) if ticket.chat_session_id else None
                }
                for ticket in tickets
            ]
            
        finally:
            db.close()
    
    async def create_chat_session(self, user_id: str, user_name: str, user_department: str) -> str:
        """Create a new chat session"""
        db = self.get_db()
        try:
            session = ChatSessionORM(
                user_id=user_id,
                user_name=user_name,
                user_department=user_department
            )
            
            db.add(session)
            db.commit()
            db.refresh(session)
            
            session_id = str(session.id)
            logger.info(f"Created chat session {session_id} for user {user_name}")
            return session_id
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating chat session: {e}")
            raise
        finally:
            db.close()
    
    async def save_chat_message(self, message_data: ChatMessageCreate) -> str:
        """Save a chat message"""
        db = self.get_db()
        try:
            message = ChatMessageORM(
                session_id=message_data.session_id,
                ticket_id=message_data.ticket_id,
                role=message_data.role,
                content=message_data.content,
                confidence_score=message_data.confidence_score,
                response_time_ms=message_data.response_time_ms,
                sources=json.dumps(message_data.sources) if message_data.sources else None,
                metadata=json.dumps(message_data.metadata) if message_data.metadata else None
            )
            
            db.add(message)
            db.commit()
            db.refresh(message)
            
            message_id = str(message.id)
            
            # Broadcast message to active connections
            await self._broadcast_update("message_created", {
                'id': message_id,
                'session_id': message_data.session_id,
                'role': message_data.role,
                'content': message_data.content,
                'timestamp': message.timestamp.isoformat()
            })
            
            return message_id
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error saving chat message: {e}")
            raise
        finally:
            db.close()
    
    def get_chat_messages(self, session_id: str) -> List[Dict[str, Any]]:
        """Get chat messages for a session"""
        db = self.get_db()
        try:
            messages = db.query(ChatMessageORM).filter(
                ChatMessageORM.session_id == session_id
            ).order_by(ChatMessageORM.timestamp.asc()).all()
            
            return [
                {
                    'id': str(message.id),
                    'session_id': str(message.session_id),
                    'role': message.role,
                    'content': message.content,
                    'timestamp': message.timestamp.isoformat(),
                    'confidence_score': message.confidence_score,
                    'sources': json.loads(message.sources) if message.sources else None,
                    'metadata': json.loads(message.metadata) if message.metadata else None
                }
                for message in messages
            ]
            
        finally:
            db.close()
    
    async def link_chat_to_ticket(self, session_id: str, ticket_id: str):
        """Link a chat session to a ticket"""
        db = self.get_db()
        try:
            # Update session
            session = db.query(ChatSessionORM).filter(ChatSessionORM.id == session_id).first()
            if session:
                session.ticket_id = ticket_id
            
            # Update messages
            db.query(ChatMessageORM).filter(ChatMessageORM.session_id == session_id).update(
                {'ticket_id': ticket_id}
            )
            
            db.commit()
            logger.info(f"Linked chat session {session_id} to ticket {ticket_id}")
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error linking chat to ticket: {e}")
            raise
        finally:
            db.close()
    
    def get_analytics(self) -> Dict[str, Any]:
        """Get system analytics"""
        db = self.get_db()
        try:
            # Ticket statistics
            total_tickets = db.query(TicketORM).count()
            open_tickets = db.query(TicketORM).filter(TicketORM.status == TicketStatus.OPEN.value).count()
            resolved_tickets = db.query(TicketORM).filter(TicketORM.status == TicketStatus.RESOLVED.value).count()
            
            # Recent activity (last 7 days)
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            recent_tickets = db.query(TicketORM).filter(TicketORM.created_at >= seven_days_ago).count()
            
            # Chat statistics
            total_sessions = db.query(ChatSessionORM).count()
            total_messages = db.query(ChatMessageORM).count()
            
            # Average resolution time (for resolved tickets)
            resolved_with_times = db.query(TicketORM).filter(
                TicketORM.status == TicketStatus.RESOLVED.value,
                TicketORM.resolved_at.isnot(None)
            ).all()
            
            avg_resolution_hours = 0
            if resolved_with_times:
                total_hours = sum([
                    (ticket.resolved_at - ticket.created_at).total_seconds() / 3600
                    for ticket in resolved_with_times
                ])
                avg_resolution_hours = total_hours / len(resolved_with_times)
            
            return {
                'total_tickets': total_tickets,
                'open_tickets': open_tickets,
                'resolved_tickets': resolved_tickets,
                'recent_tickets': recent_tickets,
                'total_sessions': total_sessions,
                'total_messages': total_messages,
                'avg_resolution_hours': round(avg_resolution_hours, 2),
                'resolution_rate': round((resolved_tickets / max(total_tickets, 1)) * 100, 1)
            }
            
        finally:
            db.close()
    
    async def _broadcast_update(self, event_type: str, data: Dict[str, Any]):
        """Broadcast real-time updates to connected clients"""
        if not self.active_connections:
            return
        
        message = {
            'event': event_type,
            'data': data,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # In a real implementation, this would use WebSockets
        # For now, we'll just log the broadcast
        logger.info(f"Broadcasting {event_type}: {data}")
        
        # Store for potential retrieval
        if not hasattr(self, '_recent_events'):
            self._recent_events = []
        
        self._recent_events.append(message)
        
        # Keep only last 100 events
        if len(self._recent_events) > 100:
            self._recent_events = self._recent_events[-100:]
    
    def get_recent_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent system events"""
        if not hasattr(self, '_recent_events'):
            return []
        
        return self._recent_events[-limit:]

# Global instance
ticket_manager = None

def get_ticket_manager() -> RealTimeTicketManager:
    """Get or create the global ticket manager instance"""
    global ticket_manager
    if ticket_manager is None:
        ticket_manager = RealTimeTicketManager()
    return ticket_manager

if __name__ == "__main__":
    # Test the ticket manager
    import asyncio
    
    async def test_ticket_manager():
        tm = RealTimeTicketManager()
        
        # Test creating a ticket
        ticket_data = TicketCreate(
            title="Test ticket",
            description="This is a test ticket",
            category="test",
            user_id="test_user",
            user_name="Test User",
            user_department="IT"
        )
        
        ticket = await tm.create_ticket(ticket_data)
        print(f"Created ticket: {ticket}")
        
        # Test getting tickets
        tickets = tm.get_tickets(user_id="test_user")
        print(f"Found {len(tickets)} tickets")
        
        # Test analytics
        analytics = tm.get_analytics()
        print(f"Analytics: {analytics}")
    
    asyncio.run(test_ticket_manager())
