"""
Enterprise Technical Support Dashboard
Advanced Streamlit dashboard for comprehensive ticket management, analytics, and monitoring
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import asyncio
import logging

# Configure page
st.set_page_config(
    page_title="Enterprise Support Dashboard",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# =============================================================================
# CONFIGURATION AND CONSTANTS
# =============================================================================

API_BASE_URL = "http://localhost:8000/api/v2"
MOCK_TOKEN = "mock-jwt-token"  # In production, implement proper authentication

# Custom CSS for enterprise styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f4e79;
        text-align: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background: linear-gradient(90deg, #f0f8ff, #e6f3ff);
        border-radius: 10px;
        border-left: 5px solid #1f4e79;
    }

    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #4CAF50;
        margin-bottom: 1rem;
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #1f4e79;
    }

    .metric-label {
        font-size: 0.9rem;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .status-good { color: #4CAF50; }
    .status-warning { color: #FF9800; }
    .status-critical { color: #F44336; }

    .sidebar .sidebar-content {
        background: #f8f9fa;
    }

    .stSelectbox > div > div {
        background-color: white;
    }
</style>
""", unsafe_allow_html=True)

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

@st.cache_data(ttl=300)  # Cache for 5 minutes
def fetch_dashboard_analytics(date_from: str = None, date_to: str = None) -> Dict[str, Any]:
    """Fetch dashboard analytics from API"""
    try:
        params = {}
        if date_from:
            params['date_from'] = date_from
        if date_to:
            params['date_to'] = date_to

        headers = {"Authorization": f"Bearer {MOCK_TOKEN}"}
        response = requests.get(f"{API_BASE_URL}/analytics/dashboard", params=params, headers=headers)

        if response.status_code == 200:
            return response.json()['data']
        else:
            st.error(f"Failed to fetch analytics: {response.status_code}")
            return get_mock_analytics()
    except Exception as e:
        logger.error(f"Error fetching analytics: {e}")
        return get_mock_analytics()

def get_mock_analytics() -> Dict[str, Any]:
    """Return mock analytics data for development"""
    return {
        "summary": {
            "total_tickets": 1247,
            "open_tickets": 89,
            "resolved_tickets": 1158,
            "avg_resolution_time_hours": 4.2,
            "sla_compliance_rate": 94.5,
            "customer_satisfaction": 4.3
        },
        "trends": {
            "tickets_created_today": 23,
            "tickets_resolved_today": 31,
            "ai_resolution_rate": 67.8,
            "automation_success_rate": 96.2
        },
        "sla_status": {
            "response_breaches": 3,
            "resolution_breaches": 1,
            "at_risk_tickets": 7
        },
        "agent_performance": [
            {"agent_name": "John Smith", "active_tickets": 12, "avg_resolution_time": 3.8, "satisfaction_rating": 4.5},
            {"agent_name": "Jane Doe", "active_tickets": 8, "avg_resolution_time": 2.9, "satisfaction_rating": 4.7},
            {"agent_name": "Mike Johnson", "active_tickets": 15, "avg_resolution_time": 5.1, "satisfaction_rating": 4.2},
            {"agent_name": "Sarah Wilson", "active_tickets": 6, "avg_resolution_time": 3.2, "satisfaction_rating": 4.8}
        ],
        "category_breakdown": [
            {"category": "Password & Access", "count": 45, "percentage": 36.2},
            {"category": "Email & Communication", "count": 32, "percentage": 25.8},
            {"category": "Software & Applications", "count": 28, "percentage": 22.6},
            {"category": "Hardware & Equipment", "count": 19, "percentage": 15.4}
        ],
        "recent_tickets": [
            {"id": "ACM-2025-000123", "subject": "Cannot access email on mobile", "priority": "Medium", "status": "Open", "created": "2025-07-06 14:30"},
            {"id": "ACM-2025-000124", "subject": "Password reset request", "priority": "Low", "status": "Resolved", "created": "2025-07-06 13:45"},
            {"id": "ACM-2025-000125", "subject": "Printer not working", "priority": "High", "status": "In Progress", "created": "2025-07-06 12:15"},
            {"id": "ACM-2025-000126", "subject": "Software installation needed", "priority": "Medium", "status": "Open", "created": "2025-07-06 11:30"}
        ]
    }

@st.cache_data(ttl=300)
def fetch_performance_metrics(metric_type: str, period: str = "7d") -> Dict[str, Any]:
    """Fetch performance metrics from API"""
    try:
        headers = {"Authorization": f"Bearer {MOCK_TOKEN}"}
        params = {"metric_type": metric_type, "period": period}
        response = requests.get(f"{API_BASE_URL}/analytics/performance", params=params, headers=headers)

        if response.status_code == 200:
            return response.json()['data']
        else:
            return get_mock_performance_data(metric_type, period)
    except Exception as e:
        logger.error(f"Error fetching performance metrics: {e}")
        return get_mock_performance_data(metric_type, period)

def get_mock_performance_data(metric_type: str, period: str) -> Dict[str, Any]:
    """Return mock performance data"""
    if metric_type == "resolution_time":
        return {
            "metric": "Average Resolution Time",
            "period": period,
            "current_value": 4.2,
            "previous_value": 5.1,
            "improvement": 17.6,
            "trend": "improving",
            "daily_values": [5.2, 4.8, 4.5, 4.1, 3.9, 4.2, 4.0]
        }
    elif metric_type == "sla_compliance":
        return {
            "metric": "SLA Compliance Rate",
            "period": period,
            "current_value": 94.5,
            "previous_value": 91.2,
            "improvement": 3.3,
            "trend": "improving",
            "daily_values": [91.2, 92.1, 93.5, 94.2, 94.8, 94.5, 95.1]
        }
    else:
        return {"metric": metric_type, "period": period, "message": "Data not available"}

def create_metric_card(title: str, value: str, delta: str = None, delta_color: str = "normal"):
    """Create a styled metric card"""
    delta_html = ""
    if delta:
        color_class = "status-good" if delta_color == "normal" else "status-critical" if delta_color == "inverse" else "status-warning"
        delta_html = f'<div class="{color_class}" style="font-size: 0.8rem; margin-top: 0.5rem;">{delta}</div>'

    st.markdown(f"""
    <div class="metric-card">
        <div class="metric-label">{title}</div>
        <div class="metric-value">{value}</div>
        {delta_html}
    </div>
    """, unsafe_allow_html=True)

# =============================================================================
# MAIN DASHBOARD
# =============================================================================

def main():
    """Main dashboard application"""

    # Header
    st.markdown('<div class="main-header">🎯 Enterprise Technical Support Dashboard</div>', unsafe_allow_html=True)

    # Sidebar
    with st.sidebar:
        st.header("📊 Dashboard Controls")

        # Date range selector
        date_range = st.selectbox(
            "Time Period",
            ["Last 7 days", "Last 30 days", "Last 90 days", "Custom"],
            index=1
        )

        if date_range == "Custom":
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input("From", datetime.now() - timedelta(days=30))
            with col2:
                end_date = st.date_input("To", datetime.now())
        else:
            days_map = {"Last 7 days": 7, "Last 30 days": 30, "Last 90 days": 90}
            days = days_map.get(date_range, 30)
            start_date = datetime.now() - timedelta(days=days)
            end_date = datetime.now()

        # Refresh button
        if st.button("🔄 Refresh Data", type="primary"):
            st.cache_data.clear()
            st.rerun()

        # View selector
        st.header("📋 Dashboard Views")
        view_mode = st.selectbox(
            "Select View",
            ["Executive Summary", "Operational Dashboard", "Agent Performance", "Analytics Deep Dive"],
            index=0
        )

        # Filters
        st.header("🔍 Filters")
        show_ai_only = st.checkbox("AI-Generated Tickets Only")
        show_sla_breached = st.checkbox("SLA Breached Only")
        priority_filter = st.multiselect(
            "Priority Levels",
            ["Low", "Medium", "High", "Critical", "Emergency"],
            default=[]
        )

    # Fetch data
    analytics = fetch_dashboard_analytics(
        start_date.isoformat() if date_range == "Custom" else None,
        end_date.isoformat() if date_range == "Custom" else None
    )

    # Display dashboard based on selected view
    if view_mode == "Executive Summary":
        display_executive_summary(analytics)
    elif view_mode == "Operational Dashboard":
        display_operational_dashboard(analytics)
    elif view_mode == "Agent Performance":
        display_agent_performance(analytics)
    elif view_mode == "Analytics Deep Dive":
        display_analytics_deep_dive(analytics)

def display_executive_summary(analytics: Dict[str, Any]):
    """Display executive summary dashboard"""
    st.header("📈 Executive Summary")

    # Key metrics row
    col1, col2, col3, col4, col5, col6 = st.columns(6)

    with col1:
        create_metric_card(
            "Total Tickets",
            f"{analytics['summary']['total_tickets']:,}",
            f"+{analytics['trends']['tickets_created_today']} today"
        )

    with col2:
        create_metric_card(
            "Open Tickets",
def display_operational_dashboard(analytics: Dict[str, Any]):
    """Display operational dashboard for day-to-day management"""
    st.header("⚙️ Operational Dashboard")

    # Alert section
    col1, col2, col3 = st.columns(3)

    with col1:
        if analytics['sla_status']['response_breaches'] > 0:
            st.error(f"🚨 {analytics['sla_status']['response_breaches']} Response SLA Breaches")
        else:
            st.success("✅ No Response SLA Breaches")

    with col2:
        if analytics['sla_status']['resolution_breaches'] > 0:
            st.error(f"⚠️ {analytics['sla_status']['resolution_breaches']} Resolution SLA Breaches")
        else:
            st.success("✅ No Resolution SLA Breaches")

    with col3:
        if analytics['sla_status']['at_risk_tickets'] > 0:
            st.warning(f"⏰ {analytics['sla_status']['at_risk_tickets']} Tickets At Risk")
        else:
            st.success("✅ No At-Risk Tickets")

    # Recent tickets table
    st.subheader("📋 Recent Tickets")
    recent_df = pd.DataFrame(analytics['recent_tickets'])

    # Style the dataframe
    def style_priority(val):
        colors = {
            'Low': 'background-color: #E8F5E8',
            'Medium': 'background-color: #FFF3CD',
            'High': 'background-color: #F8D7DA',
            'Critical': 'background-color: #F5C6CB',
            'Emergency': 'background-color: #D1ECF1'
        }
        return colors.get(val, '')

    def style_status(val):
        colors = {
            'Open': 'background-color: #D4EDDA',
            'In Progress': 'background-color: #CCE5FF',
            'Resolved': 'background-color: #D1ECF1',
            'Closed': 'background-color: #F8F9FA'
        }
        return colors.get(val, '')

    styled_df = recent_df.style.applymap(style_priority, subset=['priority']).applymap(style_status, subset=['status'])
    st.dataframe(styled_df, use_container_width=True)

    # Queue management
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 Queue Status")
        queue_data = {
            'Queue': ['L1 Support', 'L2 Support', 'L3 Support', 'Escalation'],
            'Count': [23, 8, 3, 2],
            'Avg Wait Time': ['15 min', '45 min', '2.3 hrs', '4.1 hrs']
        }
        queue_df = pd.DataFrame(queue_data)
        st.dataframe(queue_df, use_container_width=True)

    with col2:
        st.subheader("🤖 Automation Status")
        automation_data = {
            'Rule': ['Auto-assign Password', 'Escalate Critical', 'Auto-categorize', 'SLA Reminders'],
            'Status': ['Active', 'Active', 'Active', 'Active'],
            'Executions Today': [12, 3, 28, 15],
            'Success Rate': ['98%', '100%', '94%', '100%']
        }
        automation_df = pd.DataFrame(automation_data)
        st.dataframe(automation_df, use_container_width=True)

def display_agent_performance(analytics: Dict[str, Any]):
    """Display agent performance dashboard"""
    st.header("👥 Agent Performance Dashboard")

    # Agent performance table
    agent_df = pd.DataFrame(analytics['agent_performance'])

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 Agent Workload")
        fig = px.bar(
            agent_df,
            x='agent_name',
            y='active_tickets',
            title="Active Tickets by Agent",
            color='active_tickets',
            color_continuous_scale='Blues'
        )
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        st.subheader("⏱️ Resolution Time Performance")
        fig = px.scatter(
            agent_df,
            x='avg_resolution_time',
            y='satisfaction_rating',
            size='active_tickets',
            hover_name='agent_name',
            title="Resolution Time vs Satisfaction",
            labels={
                'avg_resolution_time': 'Avg Resolution Time (hours)',
                'satisfaction_rating': 'Customer Satisfaction'
            }
        )
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

    # Detailed agent metrics
    st.subheader("📈 Detailed Agent Metrics")

    # Add calculated metrics
    agent_df['efficiency_score'] = (agent_df['satisfaction_rating'] * 20) - (agent_df['avg_resolution_time'] * 2)
    agent_df['workload_status'] = agent_df['active_tickets'].apply(
        lambda x: 'High' if x > 12 else 'Medium' if x > 6 else 'Low'
    )

    # Style the agent performance table
    def color_workload(val):
        colors = {
            'Low': 'background-color: #D4EDDA',
            'Medium': 'background-color: #FFF3CD',
            'High': 'background-color: #F8D7DA'
        }
        return colors.get(val, '')

    styled_agent_df = agent_df.style.applymap(color_workload, subset=['workload_status']).format({
        'avg_resolution_time': '{:.1f}h',
        'satisfaction_rating': '{:.1f}/5',
        'efficiency_score': '{:.1f}'
    })

    st.dataframe(styled_agent_df, use_container_width=True)

    # Agent recommendations
    st.subheader("💡 Recommendations")

    high_workload_agents = agent_df[agent_df['active_tickets'] > 12]['agent_name'].tolist()
    if high_workload_agents:
        st.warning(f"⚠️ High workload detected for: {', '.join(high_workload_agents)}. Consider redistributing tickets.")

    low_satisfaction_agents = agent_df[agent_df['satisfaction_rating'] < 4.0]['agent_name'].tolist()
    if low_satisfaction_agents:
        st.info(f"📚 Training opportunity for: {', '.join(low_satisfaction_agents)}. Focus on customer service skills.")

    efficient_agents = agent_df[agent_df['efficiency_score'] > 15]['agent_name'].tolist()
    if efficient_agents:
        st.success(f"🌟 Top performers: {', '.join(efficient_agents)}. Consider them for mentoring roles.")

def display_analytics_deep_dive(analytics: Dict[str, Any]):
    """Display detailed analytics and insights"""
    st.header("🔍 Analytics Deep Dive")

    # Performance metrics selector
    col1, col2 = st.columns([1, 3])

    with col1:
        metric_type = st.selectbox(
            "Select Metric",
            ["resolution_time", "sla_compliance", "customer_satisfaction", "ai_effectiveness"]
        )

        period = st.selectbox(
            "Time Period",
            ["7d", "30d", "90d"],
            index=1
        )

    with col2:
        # Fetch and display performance data
        perf_data = fetch_performance_metrics(metric_type, period)

        if 'daily_values' in perf_data:
            # Create trend chart
            days = list(range(len(perf_data['daily_values'])))
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=days,
                y=perf_data['daily_values'],
                mode='lines+markers',
                name=perf_data['metric'],
                line=dict(color='#1f77b4', width=3)
            ))

            fig.update_layout(
                title=f"{perf_data['metric']} - {period} Trend",
                xaxis_title="Days Ago",
                yaxis_title=perf_data['metric'],
                height=400
            )

            st.plotly_chart(fig, use_container_width=True)

            # Show improvement metrics
            if perf_data.get('improvement'):
                improvement = perf_data['improvement']
                if improvement > 0:
                    st.success(f"📈 {improvement:.1f}% improvement over previous period")
                else:
                    st.error(f"📉 {abs(improvement):.1f}% decline from previous period")

    # Advanced analytics
    st.subheader("🧠 AI & Automation Insights")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "AI Resolution Rate",
            f"{analytics['trends']['ai_resolution_rate']:.1f}%",
            "2.3% vs last month"
        )

    with col2:
        st.metric(
            "Automation Success",
            f"{analytics['trends']['automation_success_rate']:.1f}%",
            "1.8% vs last month"
        )

    with col3:
        st.metric(
            "Deflection Rate",
            "73.2%",
            "5.1% vs last month"
        )

    # Predictive insights
    st.subheader("🔮 Predictive Insights")

    insights = [
        "📊 Ticket volume expected to increase 15% next week due to software update rollout",
        "🎯 Password reset automation could handle 23% more tickets with improved categorization",
        "⚡ Response time SLA at risk for 3 tickets in next 2 hours",
        "🤖 AI confidence scores suggest 12 tickets could be auto-resolved with manual review",
        "📈 Customer satisfaction trending upward (+0.3 points) due to faster resolution times"
    ]

    for insight in insights:
        st.info(insight)

    # Export options
    st.subheader("📤 Export Options")
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📊 Export Analytics Report"):
            st.success("Analytics report exported to downloads folder")

    with col2:
        if st.button("📋 Export Ticket Data"):
            st.success("Ticket data exported as CSV")

    with col3:
        if st.button("📈 Schedule Report"):
            st.success("Report scheduled for weekly delivery")

# =============================================================================
# RUN APPLICATION
# =============================================================================

if __name__ == "__main__":
    main()

    with col3:
        create_metric_card(
            "Resolution Time",
            f"{analytics['summary']['avg_resolution_time_hours']:.1f}h",
            "17.6% improvement"
        )

    with col4:
        create_metric_card(
            "SLA Compliance",
            f"{analytics['summary']['sla_compliance_rate']:.1f}%",
            "****% vs last period"
        )

    with col5:
        create_metric_card(
            "Customer Satisfaction",
            f"{analytics['summary']['customer_satisfaction']:.1f}/5",
            "⭐ Excellent"
        )

    with col6:
        create_metric_card(
            "AI Resolution Rate",
            f"{analytics['trends']['ai_resolution_rate']:.1f}%",
            "🤖 AI Powered"
        )

    # Charts row
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 Ticket Categories")
        category_df = pd.DataFrame(analytics['category_breakdown'])
        fig = px.pie(
            category_df,
            values='count',
            names='category',
            title="Tickets by Category",
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

    with col2:
        st.subheader("⚡ Performance Trends")
        # Mock trend data
        dates = pd.date_range(start=datetime.now() - timedelta(days=7), end=datetime.now(), freq='D')
        trend_data = {
            'Date': dates,
            'Tickets Created': [23, 31, 28, 35, 29, 33, 27],
            'Tickets Resolved': [31, 29, 33, 31, 35, 28, 32],
            'SLA Compliance': [94.5, 95.1, 93.8, 96.2, 94.7, 95.5, 94.9]
        }
        trend_df = pd.DataFrame(trend_data)

        fig = make_subplots(specs=[[{"secondary_y": True}]])
        fig.add_trace(
            go.Scatter(x=trend_df['Date'], y=trend_df['Tickets Created'], name="Created", line=dict(color='#FF6B6B')),
            secondary_y=False
        )
        fig.add_trace(
            go.Scatter(x=trend_df['Date'], y=trend_df['Tickets Resolved'], name="Resolved", line=dict(color='#4ECDC4')),
            secondary_y=False
        )
        fig.add_trace(
            go.Scatter(x=trend_df['Date'], y=trend_df['SLA Compliance'], name="SLA %", line=dict(color='#45B7D1')),
            secondary_y=True
        )

        fig.update_xaxes(title_text="Date")
        fig.update_yaxes(title_text="Tickets", secondary_y=False)
        fig.update_yaxes(title_text="SLA Compliance %", secondary_y=True)
        fig.update_layout(height=400, title="7-Day Performance Trend")

        st.plotly_chart(fig, use_container_width=True)
