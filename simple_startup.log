2025-07-06 15:09:52,521 - INFO - 🔍 Checking basic requirements...
2025-07-06 15:09:52,522 - INFO - ✅ Python version OK
2025-07-06 15:09:52,527 - INFO - ✅ All required files present
2025-07-06 15:09:52,528 - INFO - 🔧 Setting up environment...
2025-07-06 15:09:52,529 - INFO - 📄 Loading environment from .env file
2025-07-06 15:09:52,560 - INFO - ✅ Environment configured
2025-07-06 15:09:52,561 - INFO - 🚀 Starting Enterprise Technical Support Platform (Simple Mode)...
2025-07-06 15:09:52,561 - INFO - 🚀 Starting API service...
2025-07-06 15:09:52,575 - INFO - ⏳ Waiting for service on port 8000...
2025-07-06 15:10:18,544 - INFO - Received signal 2, shutting down services...
2025-07-06 15:10:18,545 - INFO - 🛑 Stopping all services...
2025-07-06 15:10:18,545 - INFO - 🛑 Stopping API Server...
2025-07-06 15:10:18,546 - INFO - ✅ API Server stopped
2025-07-06 15:10:18,546 - INFO - ✅ All services stopped
2025-07-06 15:10:21,530 - INFO - 🔍 Checking basic requirements...
2025-07-06 15:10:21,531 - INFO - ✅ Python version OK
2025-07-06 15:10:21,535 - INFO - ✅ All required files present
2025-07-06 15:10:21,536 - INFO - 🔧 Setting up environment...
2025-07-06 15:10:21,537 - INFO - 📄 Loading environment from .env file
2025-07-06 15:10:21,540 - INFO - ✅ Environment configured
2025-07-06 15:10:21,541 - INFO - 🚀 Starting Enterprise Technical Support Platform (Simple Mode)...
2025-07-06 15:10:21,541 - INFO - 🚀 Starting API service...
2025-07-06 15:10:21,550 - INFO - ⏳ Waiting for service on port 8000...
2025-07-06 15:10:52,791 - ERROR - ❌ Service on port 8000 failed to start within 30 seconds
2025-07-06 15:10:52,792 - INFO - 🛑 Stopping API Server...
2025-07-06 15:10:52,793 - INFO - ✅ API Server stopped
2025-07-06 15:10:52,793 - ERROR - ❌ Failed to start API service
2025-07-06 15:10:52,794 - ERROR - ❌ Failed to start services
