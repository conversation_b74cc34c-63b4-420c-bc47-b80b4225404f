@echo off
echo ========================================
echo Enterprise Technical Support Platform
echo ========================================
echo.

echo Checking environment...
if not exist "venv" (
    echo Virtual environment not found. Running setup...
    python setup_enterprise_environment.py
    if errorlevel 1 (
        echo Setup failed. Please check the error messages above.
        pause
        exit /b 1
    )
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Checking database...
python -c "import psycopg2; print('PostgreSQL driver available')" 2>nul
if errorlevel 1 (
    echo Installing database driver...
    pip install psycopg2-binary
)

echo Starting Enterprise Platform...
python start_enterprise_platform.py

echo.
echo Platform stopped. Press any key to exit...
pause >nul
