"""
Authentication and authorization module for the Technical Support Chatbot.
Handles JWT tokens, user verification, and role-based access control.
"""

import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import CryptContext
import logging

logger = logging.getLogger(__name__)

# Configuration
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-super-secret-jwt-key-change-this-in-production")
ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "1440"))

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Security scheme
security = HTTPBearer()

class AuthenticationError(Exception):
    """Custom authentication error."""
    pass

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash."""
    return pwd_context.hash(password)

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Dict[str, Any]:
    """Verify and decode a JWT token."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        
        # Check if token has expired
        exp = payload.get("exp")
        if exp is None:
            raise AuthenticationError("Token missing expiration")
        
        if datetime.utcnow() > datetime.fromtimestamp(exp):
            raise AuthenticationError("Token has expired")
        
        return payload
        
    except JWTError as e:
        logger.error(f"JWT verification error: {e}")
        raise AuthenticationError(f"Invalid token: {e}")

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Get current authenticated user from JWT token.
    This is a dependency that can be used in FastAPI endpoints.
    """
    try:
        # Extract token from credentials
        token = credentials.credentials
        
        # Verify token and get payload
        payload = verify_token(token)
        
        # Extract user information from payload
        user_id = payload.get("sub")
        if user_id is None:
            raise AuthenticationError("Token missing user ID")
        
        # For now, return a mock user. In production, you would:
        # 1. Query the database for user details
        # 2. Verify user is still active
        # 3. Return actual user data
        
        user = {
            "id": int(user_id) if user_id.isdigit() else 1,
            "user_id": user_id,
            "email": payload.get("email", "<EMAIL>"),
            "display_name": payload.get("name", "Test User"),
            "department": payload.get("department", "IT"),
            "role": payload.get("role", "user"),
            "is_active": True
        }
        
        return user
        
    except AuthenticationError as e:
        logger.error(f"Authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Unexpected auth error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error"
        )

def get_current_active_user(current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """Get current active user (dependency)."""
    if not current_user.get("is_active", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

def require_role(required_roles: list) -> callable:
    """
    Create a dependency that requires specific roles.
    
    Args:
        required_roles: List of required roles (e.g., ['admin', 'agent'])
    
    Returns:
        Dependency function
    """
    def role_checker(current_user: Dict[str, Any] = Depends(get_current_active_user)) -> Dict[str, Any]:
        user_role = current_user.get("role", "user")
        if user_role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Operation requires one of these roles: {required_roles}"
            )
        return current_user
    
    return role_checker

# Convenience dependencies for common role requirements
require_admin = require_role(["admin"])
require_agent_or_admin = require_role(["agent", "admin"])

def create_test_token(user_data: Dict[str, Any]) -> str:
    """
    Create a test token for development/testing.
    DO NOT use in production!
    """
    if os.getenv("ENVIRONMENT") == "production":
        raise ValueError("Test tokens not allowed in production")
    
    return create_access_token(data=user_data)

def mock_azure_ad_user() -> Dict[str, Any]:
    """
    Mock Azure AD user for development.
    In production, this would integrate with actual Azure AD.
    """
    return {
        "sub": "1",
        "email": "<EMAIL>",
        "name": "Test User",
        "department": "IT Support",
        "role": "user",
        "oid": "test-object-id",  # Azure AD Object ID
        "tid": "test-tenant-id",  # Azure AD Tenant ID
    }

def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[Dict[str, Any]]:
    """
    Get current user but don't require authentication.
    Returns None if no valid token provided.
    """
    if not credentials:
        return None
    
    try:
        return get_current_user(credentials)
    except HTTPException:
        return None

# Azure AD integration functions (for future implementation)
def validate_azure_ad_token(token: str) -> Dict[str, Any]:
    """
    Validate Azure AD token.
    This would integrate with Microsoft Graph API in production.
    """
    # TODO: Implement Azure AD token validation
    # 1. Verify token signature with Azure AD public keys
    # 2. Validate token claims (aud, iss, exp, etc.)
    # 3. Extract user information from token
    # 4. Return user data
    
    # For now, return mock data
    return mock_azure_ad_user()

def get_azure_ad_user_info(token: str) -> Dict[str, Any]:
    """
    Get user information from Azure AD.
    This would call Microsoft Graph API in production.
    """
    # TODO: Implement Microsoft Graph API integration
    # 1. Call Graph API with token
    # 2. Get user profile, groups, etc.
    # 3. Map to internal user structure
    
    # For now, return mock data
    return mock_azure_ad_user()

# Development/testing utilities
def create_dev_user_token() -> str:
    """Create a development user token for testing."""
    if os.getenv("ENVIRONMENT") == "production":
        raise ValueError("Development tokens not allowed in production")
    
    dev_user = {
        "sub": "dev-user-1",
        "email": "<EMAIL>",
        "name": "Developer User",
        "department": "IT Development",
        "role": "admin",  # Give admin role for testing
    }
    
    return create_access_token(data=dev_user)

# Example usage functions
def authenticate_request(token: str) -> Dict[str, Any]:
    """
    High-level function to authenticate a request.
    Can be extended to support multiple authentication methods.
    """
    try:
        # Try JWT token first
        payload = verify_token(token)
        return payload
    except AuthenticationError:
        # Could try Azure AD token validation here
        # For now, just re-raise the error
        raise

if __name__ == "__main__":
    # Test token creation and verification
    test_user = {
        "sub": "test-user",
        "email": "<EMAIL>",
        "name": "Test User",
        "role": "user"
    }
    
    # Create token
    token = create_access_token(test_user)
    print(f"Created token: {token}")
    
    # Verify token
    try:
        payload = verify_token(token)
        print(f"Token verified: {payload}")
    except AuthenticationError as e:
        print(f"Token verification failed: {e}")
