"""
Pydantic models for the Technical Support Chatbot API.
Defines request/response models and data structures.
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

class ChatRequest(BaseModel):
    """Request model for chat messages."""
    message: str = Field(..., description="User's message")
    session_id: Optional[str] = Field(None, description="Chat session ID")
    user_context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional user context")

class DocumentSource(BaseModel):
    """Model for document sources retrieved by RAG."""
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Document content chunk")
    source_path: str = Field(..., description="Path to source document")
    relevance_score: float = Field(..., description="Relevance score from vector search")
    chunk_id: str = Field(..., description="Unique chunk identifier")

class ChatResponse(BaseModel):
    """Response model for chat messages."""
    message: str = Field(..., description="Assistant's response")
    session_id: str = Field(..., description="Chat session ID")
    sources: List[DocumentSource] = Field(default_factory=list, description="Source documents used")
    confidence_score: float = Field(..., description="Confidence score of the response")
    response_time_ms: int = Field(..., description="Response time in milliseconds")
    suggestions: List[str] = Field(default_factory=list, description="Suggested follow-up actions")
    needs_escalation: bool = Field(default=False, description="Whether issue needs human escalation")

class UserFeedback(BaseModel):
    """Model for user feedback on responses."""
    message_id: str = Field(..., description="ID of the message being rated")
    rating: Optional[int] = Field(None, ge=1, le=5, description="Rating from 1-5")
    feedback_type: str = Field(..., description="Type of feedback (thumbs_up, thumbs_down, rating)")
    comment: Optional[str] = Field(None, description="Optional feedback comment")

class EscalationRequest(BaseModel):
    """Model for escalating issues to human agents."""
    session_id: str = Field(..., description="Chat session ID")
    issue_summary: str = Field(..., description="Summary of the issue")
    priority: str = Field(default="medium", description="Priority level (low, medium, high, urgent)")
    category: Optional[str] = Field(None, description="Issue category")
    user_contact: Optional[str] = Field(None, description="User contact information")

class ChatMessage(BaseModel):
    """Model for individual chat messages."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Message ID")
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")

class ChatSession(BaseModel):
    """Model for chat sessions."""
    session_id: str = Field(..., description="Session ID")
    user_id: int = Field(..., description="User ID")
    title: str = Field(..., description="Session title")
    created_at: datetime = Field(..., description="Session creation time")
    updated_at: datetime = Field(..., description="Last update time")
    is_active: bool = Field(default=True, description="Whether session is active")
    message_count: int = Field(default=0, description="Number of messages in session")

class User(BaseModel):
    """Model for users."""
    id: int = Field(..., description="User ID")
    user_id: str = Field(..., description="External user ID (e.g., Azure AD)")
    email: str = Field(..., description="User email")
    display_name: str = Field(..., description="User display name")
    department: Optional[str] = Field(None, description="User department")
    role: str = Field(default="user", description="User role (user, admin, agent)")
    is_active: bool = Field(default=True, description="Whether user is active")

class KnowledgeDocument(BaseModel):
    """Model for knowledge base documents."""
    id: int = Field(..., description="Document ID")
    title: str = Field(..., description="Document title")
    source_path: str = Field(..., description="Source file path")
    source_type: str = Field(..., description="File type (pdf, docx, html, etc.)")
    content_hash: str = Field(..., description="Content hash for deduplication")
    file_size: int = Field(..., description="File size in bytes")
    processed_at: datetime = Field(..., description="Processing timestamp")
    is_active: bool = Field(default=True, description="Whether document is active")
    chunk_count: int = Field(default=0, description="Number of chunks")

class AnalyticsData(BaseModel):
    """Model for analytics dashboard data."""
    total_sessions: int = Field(..., description="Total number of chat sessions")
    active_sessions: int = Field(..., description="Currently active sessions")
    total_messages: int = Field(..., description="Total messages exchanged")
    avg_response_time: float = Field(..., description="Average response time in seconds")
    satisfaction_score: float = Field(..., description="Average satisfaction score")
    escalation_rate: float = Field(..., description="Percentage of escalated conversations")
    top_categories: List[Dict[str, Any]] = Field(default_factory=list, description="Most common issue categories")
    resolution_rate: float = Field(..., description="Percentage of resolved issues")

class HealthStatus(BaseModel):
    """Model for system health status."""
    status: str = Field(..., description="Overall system status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Health check timestamp")
    services: Dict[str, str] = Field(default_factory=dict, description="Individual service statuses")
    version: str = Field(default="1.0.0", description="API version")

class EscalationResponse(BaseModel):
    """Response model for escalation requests."""
    status: str = Field(..., description="Escalation status")
    escalation_id: str = Field(..., description="Unique escalation ID")
    message: str = Field(..., description="Status message")
    estimated_response_time: Optional[str] = Field(None, description="Estimated response time")

class ChatHistoryResponse(BaseModel):
    """Response model for chat history."""
    session_id: str = Field(..., description="Session ID")
    messages: List[ChatMessage] = Field(..., description="List of messages in session")
    total_messages: int = Field(..., description="Total number of messages")
    session_start: datetime = Field(..., description="Session start time")

class IngestionStatus(BaseModel):
    """Model for document ingestion status."""
    document_id: int = Field(..., description="Document ID")
    status: str = Field(..., description="Processing status")
    chunks_created: int = Field(..., description="Number of chunks created")
    processing_time: float = Field(..., description="Processing time in seconds")
    error_message: Optional[str] = Field(None, description="Error message if failed")

class SearchRequest(BaseModel):
    """Model for knowledge base search requests."""
    query: str = Field(..., description="Search query")
    top_k: int = Field(default=5, ge=1, le=20, description="Number of results to return")
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Search filters")

class SearchResponse(BaseModel):
    """Model for knowledge base search responses."""
    query: str = Field(..., description="Original search query")
    results: List[DocumentSource] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results found")
    search_time_ms: int = Field(..., description="Search time in milliseconds")
