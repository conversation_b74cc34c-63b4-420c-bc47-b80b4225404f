#!/usr/bin/env python3
"""
Enterprise Technical Support Platform - Full Service Startup Script
This script starts all services for the enterprise platform in the correct order
"""

import os
import sys
import time
import subprocess
import threading
import signal
import logging
from pathlib import Path
from typing import List, Dict, Any
import psutil
import requests
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enterprise_startup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EnterpriseServiceManager:
    """Manages all enterprise platform services"""
    
    def __init__(self):
        self.services = {}
        self.base_dir = Path(__file__).parent
        self.venv_python = self._get_python_executable()
        self.running = True
        
        # Service configuration
        self.service_config = {
            'redis': {
                'command': ['redis-server', '--port', '6379'],
                'health_check': 'redis://localhost:6379',
                'port': 6379,
                'required': True
            },
            'api': {
                'command': [
                    self.venv_python, '-m', 'uvicorn', 
                    'backend.enterprise_api:app',
                    '--host', '0.0.0.0',
                    '--port', '8000',
                    '--reload'
                ],
                'health_check': 'http://localhost:8000/health',
                'port': 8000,
                'required': True,
                'depends_on': ['redis']
            },
            'dashboard': {
                'command': [
                    self.venv_python, '-m', 'streamlit', 'run',
                    'enterprise_dashboard.py',
                    '--server.port', '8501',
                    '--server.address', '0.0.0.0'
                ],
                'health_check': 'http://localhost:8501',
                'port': 8501,
                'required': True,
                'depends_on': ['api']
            }
        }
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _get_python_executable(self) -> str:
        """Get the correct Python executable"""
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            # We're in a virtual environment
            return sys.executable
        else:
            # Try to find venv python
            venv_python = self.base_dir / 'venv' / 'bin' / 'python'
            if venv_python.exists():
                return str(venv_python)
            return sys.executable
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down services...")
        self.running = False
        self.stop_all_services()
        sys.exit(0)
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met"""
        logger.info("🔍 Checking prerequisites...")
        
        # Check Python version
        if sys.version_info < (3, 9):
            logger.error("❌ Python 3.9+ required")
            return False
        logger.info("✅ Python version OK")
        
        # Check if virtual environment is activated
        if not (hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)):
            logger.warning("⚠️  Virtual environment not detected. Continuing anyway...")
        else:
            logger.info("✅ Virtual environment detected")
        
        # Check required files
        required_files = [
            'backend/enterprise_api.py',
            'backend/enterprise_ticket_manager.py',
            'enterprise_dashboard.py',
            'schema.sql'
        ]
        
        for file_path in required_files:
            if not (self.base_dir / file_path).exists():
                logger.error(f"❌ Required file missing: {file_path}")
                return False
        logger.info("✅ All required files present")
        
        # Check PostgreSQL
        try:
            result = subprocess.run(['psql', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ PostgreSQL available")
            else:
                logger.warning("⚠️  PostgreSQL not found in PATH")
        except FileNotFoundError:
            logger.warning("⚠️  PostgreSQL not found in PATH")
        
        # Check Redis
        try:
            result = subprocess.run(['redis-server', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ Redis available")
            else:
                logger.warning("⚠️  Redis not found in PATH")
        except FileNotFoundError:
            logger.warning("⚠️  Redis not found in PATH")
        
        return True
    
    def setup_environment(self):
        """Set up environment variables"""
        logger.info("🔧 Setting up environment...")
        
        # Default environment variables
        env_vars = {
            'DATABASE_URL': 'postgresql://username:password@localhost:5432/enterprise_support',
            'ORGANIZATION_ID': '00000000-0000-0000-0000-000000000001',
            'JWT_SECRET_KEY': 'your-super-secret-jwt-key-here-minimum-32-chars',
            'REDIS_URL': 'redis://localhost:6379/0',
            'API_HOST': '0.0.0.0',
            'API_PORT': '8000',
            'DASHBOARD_PORT': '8501',
            'LOG_LEVEL': 'INFO'
        }
        
        # Load from .env file if it exists
        env_file = self.base_dir / '.env'
        if env_file.exists():
            logger.info("📄 Loading environment from .env file")
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()
        
        # Set environment variables
        for key, value in env_vars.items():
            if key not in os.environ:
                os.environ[key] = value
        
        logger.info("✅ Environment configured")
    
    def check_port_availability(self, port: int) -> bool:
        """Check if a port is available"""
        for conn in psutil.net_connections():
            if conn.laddr.port == port:
                return False
        return True
    
    def wait_for_service(self, service_name: str, timeout: int = 60) -> bool:
        """Wait for a service to become healthy"""
        config = self.service_config[service_name]
        health_url = config['health_check']
        
        logger.info(f"⏳ Waiting for {service_name} to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                if health_url.startswith('http'):
                    response = requests.get(health_url, timeout=5)
                    if response.status_code == 200:
                        logger.info(f"✅ {service_name} is ready")
                        return True
                elif health_url.startswith('redis'):
                    import redis
                    r = redis.Redis.from_url(health_url)
                    r.ping()
                    logger.info(f"✅ {service_name} is ready")
                    return True
            except Exception as e:
                pass
            
            time.sleep(2)
        
        logger.error(f"❌ {service_name} failed to start within {timeout} seconds")
        return False
    
    def start_service(self, service_name: str) -> bool:
        """Start a single service"""
        config = self.service_config[service_name]
        
        # Check dependencies
        for dep in config.get('depends_on', []):
            if dep not in self.services or not self.services[dep]['healthy']:
                logger.error(f"❌ Dependency {dep} not ready for {service_name}")
                return False
        
        # Check port availability
        port = config['port']
        if not self.check_port_availability(port):
            logger.error(f"❌ Port {port} already in use for {service_name}")
            return False
        
        logger.info(f"🚀 Starting {service_name}...")
        
        try:
            # Start the process
            process = subprocess.Popen(
                config['command'],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.services[service_name] = {
                'process': process,
                'healthy': False,
                'config': config
            }
            
            # Wait for service to be ready
            if self.wait_for_service(service_name):
                self.services[service_name]['healthy'] = True
                logger.info(f"✅ {service_name} started successfully")
                return True
            else:
                self.stop_service(service_name)
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to start {service_name}: {e}")
            return False
    
    def stop_service(self, service_name: str):
        """Stop a single service"""
        if service_name in self.services:
            service = self.services[service_name]
            process = service['process']
            
            logger.info(f"🛑 Stopping {service_name}...")
            
            try:
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            
            del self.services[service_name]
            logger.info(f"✅ {service_name} stopped")
    
    def start_all_services(self) -> bool:
        """Start all services in the correct order"""
        logger.info("🚀 Starting Enterprise Technical Support Platform...")
        
        # Start services in dependency order
        service_order = ['redis', 'api', 'dashboard']
        
        for service_name in service_order:
            if not self.start_service(service_name):
                logger.error(f"❌ Failed to start {service_name}")
                return False
        
        logger.info("🎉 All services started successfully!")
        self.print_service_status()
        return True
    
    def stop_all_services(self):
        """Stop all services"""
        logger.info("🛑 Stopping all services...")
        
        # Stop in reverse order
        service_names = list(self.services.keys())
        service_names.reverse()
        
        for service_name in service_names:
            self.stop_service(service_name)
        
        logger.info("✅ All services stopped")
    
    def print_service_status(self):
        """Print current service status"""
        print("\n" + "="*60)
        print("🎯 ENTERPRISE TECHNICAL SUPPORT PLATFORM")
        print("="*60)
        print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n📊 Service Status:")
        
        for service_name, service in self.services.items():
            config = service['config']
            status = "🟢 RUNNING" if service['healthy'] else "🔴 FAILED"
            print(f"  {service_name.upper():12} | {status} | Port {config['port']}")
        
        print("\n🌐 Access URLs:")
        if 'api' in self.services and self.services['api']['healthy']:
            print("  📡 API Server:     http://localhost:8000")
            print("  📚 API Docs:      http://localhost:8000/docs")
        
        if 'dashboard' in self.services and self.services['dashboard']['healthy']:
            print("  📊 Dashboard:     http://localhost:8501")
        
        print("\n💡 Quick Commands:")
        print("  • Press Ctrl+C to stop all services")
        print("  • Check logs: tail -f enterprise_startup.log")
        print("="*60)
    
    def monitor_services(self):
        """Monitor services and restart if needed"""
        while self.running:
            for service_name, service in list(self.services.items()):
                process = service['process']
                if process.poll() is not None:  # Process has terminated
                    logger.warning(f"⚠️  {service_name} has stopped unexpectedly")
                    service['healthy'] = False
                    
                    if self.running:  # Only restart if we're not shutting down
                        logger.info(f"🔄 Restarting {service_name}...")
                        self.stop_service(service_name)
                        time.sleep(2)
                        self.start_service(service_name)
            
            time.sleep(10)  # Check every 10 seconds

def main():
    """Main function"""
    print("🎯 Enterprise Technical Support Platform Startup")
    print("=" * 50)
    
    manager = EnterpriseServiceManager()
    
    # Check prerequisites
    if not manager.check_prerequisites():
        logger.error("❌ Prerequisites not met. Please check the requirements.")
        sys.exit(1)
    
    # Setup environment
    manager.setup_environment()
    
    # Start all services
    if not manager.start_all_services():
        logger.error("❌ Failed to start services")
        sys.exit(1)
    
    # Monitor services
    try:
        manager.monitor_services()
    except KeyboardInterrupt:
        logger.info("👋 Shutdown requested by user")
    finally:
        manager.stop_all_services()

if __name__ == "__main__":
    main()
