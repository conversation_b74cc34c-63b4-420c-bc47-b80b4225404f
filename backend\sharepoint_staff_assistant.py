"""
SharePoint Staff-Side AI Assistant Integration
Provides AI assistance to IT staff for ticket resolution with SharePoint frontend
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from sqlalchemy import text
import httpx

from .database import get_db
from .rag_pipeline import RAGPipeline
from .freshworks_integration import FreshserviceIntegration, FreshserviceConfig

logger = logging.getLogger(__name__)

class TicketAnalysisRequest(BaseModel):
    """Request to analyze a ticket for AI assistance"""
    ticket_id: str = Field(..., description="Freshservice ticket ID")
    staff_id: str = Field(..., description="SharePoint staff user ID")
    staff_email: str = Field(..., description="Staff email address")
    staff_name: str = Field(..., description="Staff display name")
    include_similar_tickets: bool = Field(default=True)
    analysis_depth: str = Field(default="standard", description="basic|standard|deep")

class ResolutionSuggestion(BaseModel):
    """AI-generated resolution suggestion"""
    title: str = Field(..., description="Solution title")
    description: str = Field(..., description="Detailed solution steps")
    confidence_score: float = Field(..., description="AI confidence (0-1)")
    estimated_time: str = Field(..., description="Estimated resolution time")
    complexity: str = Field(..., description="low|medium|high")
    required_permissions: List[str] = Field(default_factory=list)
    related_articles: List[Dict[str, str]] = Field(default_factory=list)

class TicketAnalysis(BaseModel):
    """Comprehensive ticket analysis"""
    ticket_id: str
    analysis_timestamp: datetime
    ticket_summary: Dict[str, Any]
    issue_classification: Dict[str, Any]
    resolution_suggestions: List[ResolutionSuggestion]
    similar_tickets: List[Dict[str, Any]]
    escalation_recommendations: Optional[Dict[str, Any]] = None
    knowledge_gaps: List[str] = Field(default_factory=list)
    staff_notes: List[str] = Field(default_factory=list)

class StaffFeedback(BaseModel):
    """Staff feedback on AI suggestions"""
    ticket_id: str
    suggestion_id: str
    staff_id: str
    feedback_type: str = Field(..., description="helpful|not_helpful|partially_helpful")
    rating: int = Field(..., ge=1, le=5, description="1-5 rating")
    comment: Optional[str] = None
    time_saved: Optional[int] = Field(None, description="Time saved in minutes")
    resolution_successful: Optional[bool] = None

class KnowledgeUpdate(BaseModel):
    """Update to knowledge base from staff experience"""
    ticket_id: str
    staff_id: str
    solution_title: str
    solution_description: str
    category: str
    tags: List[str] = Field(default_factory=list)
    effectiveness_rating: int = Field(..., ge=1, le=5)

class SharePointStaffAssistant:
    """Staff-side AI assistant for SharePoint integration"""
    
    def __init__(self, rag_pipeline: RAGPipeline):
        self.rag_pipeline = rag_pipeline
        self.analysis_cache = {}  # In production, use Redis
        self.freshservice_config = FreshserviceConfig()
        
    async def analyze_ticket(
        self,
        request: TicketAnalysisRequest,
        db: Session
    ) -> TicketAnalysis:
        """Provide comprehensive AI analysis of a ticket"""
        
        try:
            # Get ticket details from Freshservice
            async with FreshserviceIntegration(self.freshservice_config) as fs:
                ticket_data = await fs.get_ticket(
                    int(request.ticket_id),
                    include=["conversations", "requester"]
                )
            
            ticket = ticket_data["ticket"]
            
            # Extract key information
            ticket_summary = {
                "id": ticket["id"],
                "display_id": ticket["display_id"],
                "subject": ticket["subject"],
                "description": ticket.get("description_text", ticket["description"]),
                "status": ticket["status"],
                "priority": ticket["priority"],
                "category": ticket.get("category"),
                "requester": ticket.get("requester", {}),
                "created_at": ticket["created_at"],
                "updated_at": ticket["updated_at"]
            }
            
            # Classify the issue using AI
            issue_classification = await self._classify_issue(ticket_summary)
            
            # Generate resolution suggestions
            resolution_suggestions = await self._generate_resolution_suggestions(
                ticket_summary,
                issue_classification,
                request.analysis_depth
            )
            
            # Find similar tickets
            similar_tickets = []
            if request.include_similar_tickets:
                similar_tickets = await self._find_similar_tickets(
                    ticket_summary,
                    db,
                    limit=5
                )
            
            # Check if escalation is recommended
            escalation_recommendations = await self._evaluate_escalation_need(
                ticket_summary,
                issue_classification,
                similar_tickets
            )
            
            # Identify knowledge gaps
            knowledge_gaps = await self._identify_knowledge_gaps(
                ticket_summary,
                resolution_suggestions
            )
            
            analysis = TicketAnalysis(
                ticket_id=request.ticket_id,
                analysis_timestamp=datetime.utcnow(),
                ticket_summary=ticket_summary,
                issue_classification=issue_classification,
                resolution_suggestions=resolution_suggestions,
                similar_tickets=similar_tickets,
                escalation_recommendations=escalation_recommendations,
                knowledge_gaps=knowledge_gaps
            )
            
            # Cache analysis
            self.analysis_cache[request.ticket_id] = analysis
            
            # Log staff assistance usage
            await self._log_staff_assistance(request, analysis, db)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing ticket {request.ticket_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to analyze ticket: {str(e)}"
            )
    
    async def _classify_issue(self, ticket_summary: Dict[str, Any]) -> Dict[str, Any]:
        """Use AI to classify the issue"""
        
        classification_prompt = f"""
        Analyze this IT support ticket and classify the issue:
        
        Subject: {ticket_summary['subject']}
        Description: {ticket_summary['description']}
        Category: {ticket_summary.get('category', 'Unknown')}
        
        Provide classification including:
        1. Primary issue type
        2. Complexity level (low/medium/high)
        3. Urgency assessment
        4. Required expertise level
        5. Potential root causes
        """
        
        try:
            rag_result = await self.rag_pipeline.process_query(
                classification_prompt,
                user_context={"role": "staff_analysis"}
            )
            
            # Parse AI response into structured format
            return {
                "primary_type": self._extract_primary_type(ticket_summary),
                "complexity": self._assess_complexity(ticket_summary, rag_result),
                "urgency": self._assess_urgency(ticket_summary),
                "expertise_required": self._determine_expertise_level(ticket_summary),
                "root_causes": self._identify_root_causes(rag_result.response),
                "ai_confidence": rag_result.confidence
            }
            
        except Exception as e:
            logger.error(f"Error classifying issue: {e}")
            return {
                "primary_type": "Unknown",
                "complexity": "medium",
                "urgency": "medium",
                "expertise_required": "Level 1",
                "root_causes": ["Unable to determine"],
                "ai_confidence": 0.0
            }
    
    async def _generate_resolution_suggestions(
        self,
        ticket_summary: Dict[str, Any],
        classification: Dict[str, Any],
        depth: str
    ) -> List[ResolutionSuggestion]:
        """Generate AI-powered resolution suggestions"""
        
        suggestions = []
        
        try:
            # Create resolution query
            resolution_query = f"""
            Provide step-by-step resolution suggestions for this IT issue:
            
            Issue: {ticket_summary['subject']}
            Description: {ticket_summary['description']}
            Type: {classification['primary_type']}
            Complexity: {classification['complexity']}
            
            Please provide multiple resolution approaches ranked by likelihood of success.
            """
            
            rag_result = await self.rag_pipeline.process_query(
                resolution_query,
                user_context={"role": "staff_resolution", "depth": depth}
            )
            
            # Generate multiple suggestions based on analysis depth
            suggestion_count = {"basic": 2, "standard": 3, "deep": 5}[depth]
            
            for i in range(suggestion_count):
                suggestion = ResolutionSuggestion(
                    title=f"Resolution Option {i+1}",
                    description=self._extract_solution_steps(rag_result.response, i),
                    confidence_score=max(0.1, rag_result.confidence - (i * 0.1)),
                    estimated_time=self._estimate_resolution_time(classification, i),
                    complexity=classification['complexity'],
                    required_permissions=self._determine_required_permissions(
                        ticket_summary,
                        classification
                    ),
                    related_articles=rag_result.sources[:3] if rag_result.sources else []
                )
                suggestions.append(suggestion)
            
        except Exception as e:
            logger.error(f"Error generating resolution suggestions: {e}")
            
            # Fallback suggestion
            suggestions.append(
                ResolutionSuggestion(
                    title="Standard Troubleshooting",
                    description="Follow standard troubleshooting procedures for this issue type.",
                    confidence_score=0.5,
                    estimated_time="30-60 minutes",
                    complexity="medium",
                    required_permissions=["basic_support"]
                )
            )
        
        return suggestions
    
    async def _find_similar_tickets(
        self,
        ticket_summary: Dict[str, Any],
        db: Session,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Find similar resolved tickets for reference"""
        
        try:
            # Search for similar tickets in local database
            query = text("""
                SELECT 
                    ticket_id,
                    subject,
                    description,
                    category,
                    status,
                    resolution,
                    created_date,
                    resolved_date,
                    CASE 
                        WHEN subject ILIKE :subject_pattern THEN 3
                        WHEN description ILIKE :desc_pattern THEN 2
                        WHEN category = :category THEN 1
                        ELSE 0
                    END as similarity_score
                FROM test_tickets
                WHERE status IN ('resolved', 'closed')
                    AND (
                        subject ILIKE :subject_pattern OR
                        description ILIKE :desc_pattern OR
                        category = :category
                    )
                    AND ticket_id != :current_ticket
                ORDER BY similarity_score DESC, created_date DESC
                LIMIT :limit
            """)
            
            # Create search patterns
            subject_words = ticket_summary['subject'].split()[:3]  # First 3 words
            subject_pattern = f"%{' '.join(subject_words)}%"
            desc_pattern = f"%{ticket_summary['description'][:50]}%"
            
            result = db.execute(query, {
                "subject_pattern": subject_pattern,
                "desc_pattern": desc_pattern,
                "category": ticket_summary.get('category', ''),
                "current_ticket": f"FS-{ticket_summary['display_id']}",
                "limit": limit
            })
            
            similar_tickets = []
            for row in result:
                similar_tickets.append({
                    "ticket_id": row[0],
                    "subject": row[1],
                    "description": row[2][:100] + "..." if len(row[2]) > 100 else row[2],
                    "category": row[3],
                    "status": row[4],
                    "resolution": row[5][:200] + "..." if row[5] and len(row[5]) > 200 else row[5],
                    "similarity_score": row[8],
                    "resolved_date": row[7]
                })
            
            return similar_tickets
            
        except Exception as e:
            logger.error(f"Error finding similar tickets: {e}")
            return []
    
    async def _evaluate_escalation_need(
        self,
        ticket_summary: Dict[str, Any],
        classification: Dict[str, Any],
        similar_tickets: List[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Evaluate if ticket should be escalated"""
        
        escalation_factors = []
        escalation_score = 0
        
        # Check complexity
        if classification['complexity'] == 'high':
            escalation_factors.append("High complexity issue")
            escalation_score += 3
        
        # Check expertise requirement
        if classification['expertise_required'] in ['Level 3', 'Specialist']:
            escalation_factors.append("Requires specialized expertise")
            escalation_score += 4
        
        # Check if similar tickets failed resolution
        failed_similar = [t for t in similar_tickets if not t.get('resolution')]
        if len(failed_similar) > 2:
            escalation_factors.append("Multiple similar tickets unresolved")
            escalation_score += 2
        
        # Check ticket age and priority
        priority_map = {1: 'low', 2: 'medium', 3: 'high', 4: 'urgent'}
        priority = priority_map.get(ticket_summary['priority'], 'medium')
        
        if priority in ['high', 'urgent']:
            escalation_factors.append(f"High priority ({priority})")
            escalation_score += 2
        
        # Check if AI confidence is low
        if classification['ai_confidence'] < 0.6:
            escalation_factors.append("Low AI confidence in resolution")
            escalation_score += 1
        
        if escalation_score >= 4:
            return {
                "recommended": True,
                "reason": f"Escalation score: {escalation_score}/10",
                "factors": escalation_factors,
                "suggested_level": "Level 2" if escalation_score < 7 else "Level 3",
                "urgency": "high" if escalation_score >= 7 else "medium"
            }
        
        return None
    
    async def _identify_knowledge_gaps(
        self,
        ticket_summary: Dict[str, Any],
        suggestions: List[ResolutionSuggestion]
    ) -> List[str]:
        """Identify potential knowledge base gaps"""
        
        gaps = []
        
        # Check if AI confidence is consistently low
        avg_confidence = sum(s.confidence_score for s in suggestions) / len(suggestions) if suggestions else 0
        
        if avg_confidence < 0.6:
            gaps.append(f"Limited knowledge for issue type: {ticket_summary.get('category', 'Unknown')}")
        
        # Check for missing documentation patterns
        if 'error' in ticket_summary['description'].lower() and avg_confidence < 0.7:
            gaps.append("Error-specific troubleshooting documentation may be incomplete")
        
        if not suggestions or len(suggestions) < 2:
            gaps.append("Insufficient resolution procedures documented")
        
        return gaps
    
    async def record_staff_feedback(
        self,
        feedback: StaffFeedback,
        db: Session
    ) -> Dict[str, str]:
        """Record staff feedback on AI suggestions"""
        
        try:
            # Store feedback in database
            insert_query = text("""
                INSERT INTO staff_feedback (
                    ticket_id, suggestion_id, staff_id, feedback_type, rating,
                    comment, time_saved, resolution_successful, created_at
                ) VALUES (
                    :ticket_id, :suggestion_id, :staff_id, :feedback_type, :rating,
                    :comment, :time_saved, :resolution_successful, CURRENT_TIMESTAMP
                )
            """)
            
            db.execute(insert_query, {
                "ticket_id": feedback.ticket_id,
                "suggestion_id": feedback.suggestion_id,
                "staff_id": feedback.staff_id,
                "feedback_type": feedback.feedback_type,
                "rating": feedback.rating,
                "comment": feedback.comment,
                "time_saved": feedback.time_saved,
                "resolution_successful": feedback.resolution_successful
            })
            
            db.commit()
            
            # Update AI model if needed (future enhancement)
            await self._update_ai_model_from_feedback(feedback)
            
            return {
                "status": "recorded",
                "message": "Feedback recorded successfully"
            }
            
        except Exception as e:
            logger.error(f"Error recording staff feedback: {e}")
            db.rollback()
            raise HTTPException(
                status_code=500,
                detail="Failed to record feedback"
            )
    
    async def update_knowledge_base(
        self,
        update: KnowledgeUpdate,
        db: Session
    ) -> Dict[str, str]:
        """Update knowledge base with staff insights"""
        
        try:
            # Store knowledge update
            insert_query = text("""
                INSERT INTO knowledge_updates (
                    ticket_id, staff_id, solution_title, solution_description,
                    category, tags, effectiveness_rating, created_at
                ) VALUES (
                    :ticket_id, :staff_id, :solution_title, :solution_description,
                    :category, :tags, :effectiveness_rating, CURRENT_TIMESTAMP
                )
            """)
            
            db.execute(insert_query, {
                "ticket_id": update.ticket_id,
                "staff_id": update.staff_id,
                "solution_title": update.solution_title,
                "solution_description": update.solution_description,
                "category": update.category,
                "tags": json.dumps(update.tags),
                "effectiveness_rating": update.effectiveness_rating
            })
            
            db.commit()
            
            # Trigger knowledge base reindexing (if using vector store)
            await self._trigger_knowledge_reindex(update)
            
            return {
                "status": "updated",
                "message": "Knowledge base updated successfully"
            }
            
        except Exception as e:
            logger.error(f"Error updating knowledge base: {e}")
            db.rollback()
            raise HTTPException(
                status_code=500,
                detail="Failed to update knowledge base"
            )
    
    async def get_staff_analytics(
        self,
        staff_id: str,
        days_back: int = 30,
        db: Session = None
    ) -> Dict[str, Any]:
        """Get analytics for staff AI assistance usage"""
        
        try:
            # Query staff usage analytics
            query = text("""
                SELECT 
                    COUNT(*) as total_analyses,
                    AVG(CASE WHEN sf.rating IS NOT NULL THEN sf.rating END) as avg_rating,
                    SUM(CASE WHEN sf.time_saved IS NOT NULL THEN sf.time_saved ELSE 0 END) as total_time_saved,
                    COUNT(CASE WHEN sf.feedback_type = 'helpful' THEN 1 END) as helpful_count,
                    COUNT(CASE WHEN sf.resolution_successful = true THEN 1 END) as successful_resolutions
                FROM staff_feedback sf
                WHERE sf.staff_id = :staff_id
                    AND sf.created_at > CURRENT_TIMESTAMP - INTERVAL :days DAY
            """)
            
            result = db.execute(query, {
                "staff_id": staff_id,
                "days": days_back
            }).fetchone()
            
            return {
                "staff_id": staff_id,
                "period_days": days_back,
                "total_analyses": result[0] if result[0] else 0,
                "average_rating": round(result[1], 2) if result[1] else 0,
                "total_time_saved_minutes": result[2] if result[2] else 0,
                "helpful_suggestions": result[3] if result[3] else 0,
                "successful_resolutions": result[4] if result[4] else 0,
                "efficiency_score": self._calculate_efficiency_score(result)
            }
            
        except Exception as e:
            logger.error(f"Error getting staff analytics: {e}")
            return {
                "staff_id": staff_id,
                "error": "Unable to retrieve analytics"
            }
    
    # Helper methods
    def _extract_primary_type(self, ticket_summary: Dict[str, Any]) -> str:
        """Extract primary issue type from ticket"""
        subject = ticket_summary['subject'].lower()
        description = ticket_summary['description'].lower()
        
        # Enhanced classification with more specific keywords and priority order
        types = {
            'server': ['server', 'database', 'sql', 'oracle', 'mysql', 'postgresql'],
            'network': ['network', 'internet', 'connection', 'wifi', 'vpn', 'firewall', 'router'],
            'password': ['password', 'login', 'authentication', 'account locked', 'reset'],
            'email': ['email', 'outlook', 'mail', 'exchange', 'calendar', '@'],
            'software': ['software', 'application', 'program', 'install', 'update', 'excel', 'word'],
            'hardware': ['hardware', 'device', 'computer', 'laptop', 'printer', 'monitor'],
            'access': ['access', 'permission', 'folder', 'file', 'sharepoint', 'denied']
        }
        
        # Check in priority order (most specific first)
        for issue_type, keywords in types.items():
            if any(keyword in subject or keyword in description for keyword in keywords):
                return issue_type.title()
        
        return ticket_summary.get('category', 'General')
    
    def _assess_complexity(self, ticket_summary: Dict[str, Any], rag_result) -> str:
        """Assess issue complexity"""
        if rag_result and rag_result.confidence < 0.5:
            return "high"
        elif len(ticket_summary['description']) > 500:
            return "medium"
        else:
            return "low"
    
    def _assess_urgency(self, ticket_summary: Dict[str, Any]) -> str:
        """Assess issue urgency"""
        priority_map = {1: 'low', 2: 'medium', 3: 'high', 4: 'urgent'}
        return priority_map.get(ticket_summary.get('priority', 2), 'medium')
    
    def _determine_expertise_level(self, ticket_summary: Dict[str, Any]) -> str:
        """Determine required expertise level"""
        subject = ticket_summary['subject'].lower()
        
        if any(word in subject for word in ['server', 'database', 'network', 'security']):
            return "Level 3"
        elif any(word in subject for word in ['software', 'application', 'system']):
            return "Level 2"
        else:
            return "Level 1"
    
    def _identify_root_causes(self, ai_response: str) -> List[str]:
        """Extract potential root causes from AI response"""
        # Simple extraction - in production, use more sophisticated NLP
        causes = []
        if 'configuration' in ai_response.lower():
            causes.append('Configuration issue')
        if 'permission' in ai_response.lower():
            causes.append('Permission problem')
        if 'update' in ai_response.lower():
            causes.append('Software update needed')
        
        return causes if causes else ['Unknown']
    
    def _extract_solution_steps(self, ai_response: str, index: int) -> str:
        """Extract solution steps from AI response"""
        # Simplified - in production, parse structured response
        return f"Solution approach {index + 1} based on AI analysis: {ai_response[:200]}..."
    
    def _estimate_resolution_time(self, classification: Dict[str, Any], index: int) -> str:
        """Estimate resolution time"""
        base_times = {
            'low': ['15-30 minutes', '30-45 minutes'],
            'medium': ['30-60 minutes', '1-2 hours'],
            'high': ['2-4 hours', '4-8 hours']
        }
        
        complexity = classification.get('complexity', 'medium')
        times = base_times[complexity]
        return times[min(index, len(times) - 1)]
    
    def _determine_required_permissions(
        self, 
        ticket_summary: Dict[str, Any], 
        classification: Dict[str, Any]
    ) -> List[str]:
        """Determine required permissions for resolution"""
        permissions = ['basic_support']
        
        if classification['expertise_required'] == 'Level 2':
            permissions.append('advanced_support')
        elif classification['expertise_required'] == 'Level 3':
            permissions.extend(['advanced_support', 'system_admin'])
        
        return permissions
    
    async def _log_staff_assistance(
        self, 
        request: TicketAnalysisRequest, 
        analysis: TicketAnalysis, 
        db: Session
    ):
        """Log staff assistance usage for analytics"""
        try:
            insert_query = text("""
                INSERT INTO staff_assistance_log (
                    ticket_id, staff_id, analysis_depth, suggestions_count,
                    analysis_timestamp, created_at
                ) VALUES (
                    :ticket_id, :staff_id, :analysis_depth, :suggestions_count,
                    :analysis_timestamp, CURRENT_TIMESTAMP
                )
            """)
            
            db.execute(insert_query, {
                "ticket_id": request.ticket_id,
                "staff_id": request.staff_id,
                "analysis_depth": request.analysis_depth,
                "suggestions_count": len(analysis.resolution_suggestions),
                "analysis_timestamp": analysis.analysis_timestamp
            })
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Error logging staff assistance: {e}")
            # Don't fail the main operation
    
    async def _update_ai_model_from_feedback(self, feedback: StaffFeedback):
        """Update AI model based on staff feedback (future enhancement)"""
        # Placeholder for future ML model updates
        pass
    
    async def _trigger_knowledge_reindex(self, update: KnowledgeUpdate):
        """Trigger knowledge base reindexing (future enhancement)"""
        # Placeholder for vector store reindexing
        pass
    
    def _calculate_efficiency_score(self, analytics_result) -> float:
        """Calculate staff efficiency score"""
        if not analytics_result or not analytics_result[0]:
            return 0.0
        
        total_analyses = analytics_result[0]
        avg_rating = analytics_result[1] or 0
        helpful_count = analytics_result[3] or 0
        
        # Simple efficiency calculation
        rating_score = (avg_rating / 5.0) * 0.4
        helpful_ratio = (helpful_count / total_analyses) * 0.6
        
        return round((rating_score + helpful_ratio) * 100, 1)

# FastAPI routes for staff assistant
staff_app = FastAPI()

@staff_app.post("/api/v1/staff-assistant/analyze")
async def analyze_ticket(
    request: TicketAnalysisRequest,
    db: Session = Depends(get_db)
):
    """Analyze ticket and provide AI assistance to staff"""
    
    rag_pipeline = RAGPipeline()
    assistant = SharePointStaffAssistant(rag_pipeline)
    
    analysis = await assistant.analyze_ticket(request, db)
    return analysis

@staff_app.post("/api/v1/staff-assistant/feedback") 
async def record_feedback(
    feedback: StaffFeedback,
    db: Session = Depends(get_db)
):
    """Record staff feedback on AI suggestions"""
    
    rag_pipeline = RAGPipeline()
    assistant = SharePointStaffAssistant(rag_pipeline)
    
    result = await assistant.record_staff_feedback(feedback, db)
    return result

@staff_app.post("/api/v1/staff-assistant/knowledge-update")
async def update_knowledge(
    update: KnowledgeUpdate,
    db: Session = Depends(get_db)
):
    """Update knowledge base with staff insights"""
    
    rag_pipeline = RAGPipeline()
    assistant = SharePointStaffAssistant(rag_pipeline)
    
    result = await assistant.update_knowledge_base(update, db)
    return result

@staff_app.get("/api/v1/staff-assistant/analytics/{staff_id}")
async def get_analytics(
    staff_id: str,
    days_back: int = 30,
    db: Session = Depends(get_db)
):
    """Get staff AI assistance analytics"""
    
    rag_pipeline = RAGPipeline()
    assistant = SharePointStaffAssistant(rag_pipeline)
    
    analytics = await assistant.get_staff_analytics(staff_id, days_back, db)
    return analytics