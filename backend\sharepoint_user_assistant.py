"""
SharePoint User-Side AI Assistant Integration
Provides AI assistance to users before ticket creation with SharePoint frontend
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
import httpx

from .database import get_db
from .rag_pipeline import RAGPipeline
from .freshworks_integration import FreshserviceIntegration, create_ticket_from_escalation

logger = logging.getLogger(__name__)

class UserQuery(BaseModel):
    """User query model for AI assistance"""
    user_id: str = Field(..., description="SharePoint user ID")
    user_email: str = Field(..., description="User email address")
    user_name: str = Field(..., description="User display name")
    query: str = Field(..., description="User's support question")
    session_id: Optional[str] = None
    department: Optional[str] = None
    urgency_level: Optional[str] = "medium"

class AIResponse(BaseModel):
    """AI assistant response model"""
    response: str = Field(..., description="AI generated response")
    confidence_score: float = Field(..., description="AI confidence level (0-1)")
    suggested_category: Optional[str] = None
    requires_escalation: bool = Field(default=False)
    escalation_reason: Optional[str] = None
    suggested_actions: List[str] = Field(default_factory=list)
    knowledge_sources: List[Dict[str, str]] = Field(default_factory=list)
    session_id: str = Field(..., description="Chat session identifier")

class EscalationRequest(BaseModel):
    """Request to escalate to human support"""
    session_id: str
    user_feedback: Optional[str] = None
    issue_not_resolved: bool = True
    urgency_increase: bool = False

class SharePointUserAssistant:
    """User-side AI assistant for SharePoint integration"""
    
    def __init__(self, rag_pipeline: RAGPipeline):
        self.rag_pipeline = rag_pipeline
        self.escalation_threshold = 0.7  # Below this confidence, consider escalation
        self.session_store = {}  # In production, use Redis or similar
        
    async def process_user_query(
        self, 
        query: UserQuery, 
        db: Session
    ) -> AIResponse:
        """Process user query and provide AI assistance"""
        
        # Generate session ID if not provided
        session_id = query.session_id or f"user_{query.user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Store session data
        self.session_store[session_id] = {
            "user_id": query.user_id,
            "user_email": query.user_email,
            "user_name": query.user_name,
            "department": query.department,
            "started_at": datetime.utcnow(),
            "queries": []
        }
        
        try:
            # Use RAG pipeline to get AI response
            rag_result = await self.rag_pipeline.process_query(
                query.query,
                user_context={
                    "user_email": query.user_email,
                    "department": query.department,
                    "urgency": query.urgency_level
                }
            )
            
            # Determine if escalation is needed
            requires_escalation = rag_result.confidence < self.escalation_threshold
            escalation_reason = None
            
            if requires_escalation:
                escalation_reason = f"Low confidence ({rag_result.confidence:.2%}) - may need human expertise"
            
            # Categorize the issue
            suggested_category = self._categorize_query(query.query, rag_result.sources)
            
            # Generate suggested actions
            suggested_actions = self._generate_suggested_actions(
                query.query, 
                rag_result.response, 
                suggested_category
            )
            
            # Store query in session
            self.session_store[session_id]["queries"].append({
                "timestamp": datetime.utcnow(),
                "query": query.query,
                "response": rag_result.response,
                "confidence": rag_result.confidence,
                "category": suggested_category
            })
            
            # Prepare knowledge sources for SharePoint display
            knowledge_sources = [
                {
                    "title": source.get("title", "Knowledge Base"),
                    "url": source.get("url", "#"),
                    "snippet": source.get("content", "")[:200] + "..."
                }
                for source in rag_result.sources[:3]  # Top 3 sources
            ]
            
            return AIResponse(
                response=rag_result.response,
                confidence_score=rag_result.confidence,
                suggested_category=suggested_category,
                requires_escalation=requires_escalation,
                escalation_reason=escalation_reason,
                suggested_actions=suggested_actions,
                knowledge_sources=knowledge_sources,
                session_id=session_id
            )
            
        except Exception as e:
            logger.error(f"Error processing user query: {e}")
            
            # Fallback response with escalation
            return AIResponse(
                response="I'm having trouble processing your request right now. Let me connect you with human support.",
                confidence_score=0.0,
                requires_escalation=True,
                escalation_reason="System error - needs human assistance",
                suggested_actions=["Contact IT support directly", "Try again later"],
                session_id=session_id
            )
    
    def _categorize_query(self, query: str, sources: List[Dict]) -> str:
        """Categorize user query based on content and sources"""
        
        query_lower = query.lower()
        
        # Simple keyword-based categorization
        categories = {
            "password": ["password", "login", "account", "authentication", "sign in"],
            "email": ["email", "outlook", "mail", "exchange", "calendar"],
            "software": ["software", "application", "program", "install", "update"],
            "hardware": ["computer", "laptop", "printer", "monitor", "keyboard"],
            "network": ["internet", "wifi", "network", "connection", "vpn"],
            "access": ["access", "permission", "folder", "file", "sharepoint"]
        }
        
        for category, keywords in categories.items():
            if any(keyword in query_lower for keyword in keywords):
                return category.title()
        
        return "General"
    
    def _generate_suggested_actions(
        self, 
        query: str, 
        ai_response: str, 
        category: str
    ) -> List[str]:
        """Generate suggested actions based on query and category"""
        
        actions = []
        
        # Category-specific actions
        if category == "Password":
            actions.extend([
                "Try using the self-service password reset portal",
                "Check if Caps Lock is enabled",
                "Contact your manager if account is locked"
            ])
        elif category == "Email":
            actions.extend([
                "Check your internet connection",
                "Try accessing email from a web browser",
                "Restart your email application"
            ])
        elif category == "Software":
            actions.extend([
                "Check if you have admin rights for installation",
                "Try restarting your computer",
                "Contact IT for software licensing"
            ])
        elif category == "Hardware":
            actions.extend([
                "Check all cable connections",
                "Try restarting the device",
                "Contact facilities for hardware replacement"
            ])
        
        # General actions
        actions.extend([
            "Document any error messages you see",
            "Note when the issue started occurring",
            "Try the solution again if it didn't work the first time"
        ])
        
        return actions[:4]  # Limit to 4 actions
    
    async def escalate_to_human_support(
        self,
        escalation: EscalationRequest,
        db: Session
    ) -> Dict[str, Any]:
        """Escalate user issue to human support via Freshservice"""
        
        if escalation.session_id not in self.session_store:
            raise HTTPException(
                status_code=404,
                detail="Session not found"
            )
        
        session_data = self.session_store[escalation.session_id]
        
        # Prepare escalation data
        issue_summary = self._generate_issue_summary(session_data, escalation)
        
        escalation_data = {
            "session_id": escalation.session_id,
            "user_email": session_data["user_email"],
            "user_name": session_data["user_name"],
            "issue_summary": issue_summary,
            "category": self._get_most_common_category(session_data["queries"]),
            "priority": self._determine_priority(escalation, session_data),
            "confidence_score": self._calculate_session_confidence(session_data["queries"]),
            "department": session_data.get("department")
        }
        
        try:
            # Create Freshservice ticket
            ticket_result = await create_ticket_from_escalation(escalation_data, db)
            
            # Update session with ticket info
            session_data["escalated"] = True
            session_data["ticket_id"] = ticket_result["ticket"]["id"]
            session_data["ticket_display_id"] = ticket_result["ticket"]["display_id"]
            session_data["escalated_at"] = datetime.utcnow()
            
            return {
                "status": "escalated",
                "ticket_id": ticket_result["ticket"]["display_id"],
                "ticket_url": f"https://your-company.freshservice.com/helpdesk/tickets/{ticket_result['ticket']['display_id']}",
                "message": f"Your issue has been escalated to human support. Ticket #{ticket_result['ticket']['display_id']} has been created.",
                "estimated_response_time": "2-4 hours during business hours"
            }
            
        except Exception as e:
            logger.error(f"Error escalating to human support: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to escalate to human support"
            )
    
    def _generate_issue_summary(
        self, 
        session_data: Dict, 
        escalation: EscalationRequest
    ) -> str:
        """Generate comprehensive issue summary for ticket"""
        
        queries = session_data.get("queries", [])
        
        summary = f"""
        **User Support Session Summary**
        
        **User:** {session_data['user_name']} ({session_data['user_email']})
        **Department:** {session_data.get('department', 'Not specified')}
        **Session Started:** {session_data['started_at'].strftime('%Y-%m-%d %H:%M UTC')}
        **Total Queries:** {len(queries)}
        
        **User Queries and AI Responses:**
        """
        
        for i, query_data in enumerate(queries[-3:], 1):  # Last 3 queries
            summary += f"""
        
        **Query {i}:** {query_data['query']}
        **AI Response:** {query_data['response'][:200]}...
        **Confidence:** {query_data['confidence']:.1%}
        **Category:** {query_data.get('category', 'Unknown')}
        """
        
        if escalation.user_feedback:
            summary += f"""
        
        **User Feedback:** {escalation.user_feedback}
        """
        
        summary += f"""
        
        **Escalation Reason:** {"Issue not resolved by AI assistance" if escalation.issue_not_resolved else "User requested human support"}
        
        **Recommended Actions:**
        - Review AI responses for accuracy
        - Consider updating knowledge base if gaps identified
        - Follow up with user after resolution
        """
        
        return summary
    
    def _get_most_common_category(self, queries: List[Dict]) -> str:
        """Get the most common category from session queries"""
        
        if not queries:
            return "General"
        
        categories = [q.get("category", "General") for q in queries]
        return max(set(categories), key=categories.count)
    
    def _determine_priority(
        self, 
        escalation: EscalationRequest, 
        session_data: Dict
    ) -> str:
        """Determine ticket priority based on escalation context"""
        
        if escalation.urgency_increase:
            return "high"
        
        # Check if multiple failed attempts
        query_count = len(session_data.get("queries", []))
        if query_count >= 3:
            return "medium"
        
        return "medium"  # Default priority
    
    def _calculate_session_confidence(self, queries: List[Dict]) -> float:
        """Calculate average confidence score for the session"""
        
        if not queries:
            return 0.0
        
        confidences = [q.get("confidence", 0.0) for q in queries]
        return sum(confidences) / len(confidences)
    
    async def get_session_history(self, session_id: str) -> Dict[str, Any]:
        """Get session history for SharePoint display"""
        
        if session_id not in self.session_store:
            raise HTTPException(
                status_code=404,
                detail="Session not found"
            )
        
        session_data = self.session_store[session_id]
        
        return {
            "session_id": session_id,
            "user_info": {
                "name": session_data["user_name"],
                "email": session_data["user_email"],
                "department": session_data.get("department")
            },
            "started_at": session_data["started_at"],
            "query_count": len(session_data.get("queries", [])),
            "queries": session_data.get("queries", []),
            "escalated": session_data.get("escalated", False),
            "ticket_id": session_data.get("ticket_display_id")
        }

# FastAPI routes for SharePoint integration
app = FastAPI()

@app.post("/api/v1/user-assistant/query")
async def process_user_query(
    query: UserQuery,
    db: Session = Depends(get_db)
):
    """Process user query through AI assistant"""
    
    # Initialize RAG pipeline (in production, this would be a singleton)
    rag_pipeline = RAGPipeline()
    assistant = SharePointUserAssistant(rag_pipeline)
    
    response = await assistant.process_user_query(query, db)
    return response

@app.post("/api/v1/user-assistant/escalate")
async def escalate_to_support(
    escalation: EscalationRequest,
    db: Session = Depends(get_db)
):
    """Escalate user issue to human support"""
    
    rag_pipeline = RAGPipeline()
    assistant = SharePointUserAssistant(rag_pipeline)
    
    result = await assistant.escalate_to_human_support(escalation, db)
    return result

@app.get("/api/v1/user-assistant/session/{session_id}")
async def get_session_history(session_id: str):
    """Get session history for display"""
    
    rag_pipeline = RAGPipeline()
    assistant = SharePointUserAssistant(rag_pipeline)
    
    history = await assistant.get_session_history(session_id)
    return history