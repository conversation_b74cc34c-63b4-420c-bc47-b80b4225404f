#!/usr/bin/env python3
"""
Test the built-in platform functionality
"""

import urllib.request
import json

def test_health():
    """Test health endpoint"""
    try:
        response = urllib.request.urlopen('http://127.0.0.1:8000/health')
        result = json.loads(response.read().decode())
        print("✅ Health check passed")
        print(f"   Status: {result['status']}")
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_get_tickets():
    """Test getting tickets"""
    try:
        response = urllib.request.urlopen('http://127.0.0.1:8000/api/tickets')
        result = json.loads(response.read().decode())
        print("✅ Get tickets successful")
        print(f"   Total tickets: {result['count']}")
        for ticket in result['tickets']:
            print(f"   - {ticket['ticket_number']}: {ticket['subject']}")
        return True
    except Exception as e:
        print(f"❌ Get tickets failed: {e}")
        return False

def test_create_ticket():
    """Test creating a ticket"""
    try:
        data = {
            'subject': 'Test ticket from automated test',
            'description': 'This is a test ticket to verify platform functionality',
            'requester_name': 'Automated Test',
            'requester_email': '<EMAIL>',
            'priority': 'high'
        }
        
        req = urllib.request.Request(
            'http://127.0.0.1:8000/api/tickets',
            data=json.dumps(data).encode('utf-8'),
            headers={'Content-Type': 'application/json'}
        )
        
        response = urllib.request.urlopen(req)
        result = json.loads(response.read().decode())
        print("✅ Create ticket successful")
        print(f"   Ticket Number: {result['ticket_number']}")
        print(f"   Ticket ID: {result['id']}")
        return result['id']
    except Exception as e:
        print(f"❌ Create ticket failed: {e}")
        return None

def main():
    """Run all tests"""
    print("🧪 Testing Enterprise Platform (Built-in Mode)")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Health check
    if test_health():
        tests_passed += 1
    
    # Test 2: Create ticket
    ticket_id = test_create_ticket()
    if ticket_id:
        tests_passed += 1
    
    # Test 3: Get tickets
    if test_get_tickets():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Platform is working correctly.")
        print("\n🌐 Access the platform:")
        print("   Dashboard: http://127.0.0.1:8000")
        print("   API: http://127.0.0.1:8000/api/tickets")
    else:
        print("❌ Some tests failed. Check the platform status.")

if __name__ == "__main__":
    main()
