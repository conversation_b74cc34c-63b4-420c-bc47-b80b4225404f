# 🚀 Enterprise Technical Support Platform - Full Service Startup Guide

## 📋 Quick Start (5 Minutes)

### Option 1: Automated Setup (Recommended)

```bash
# 1. Run environment setup
python setup_enterprise_environment.py

# 2. Set up database
python setup_database.py

# 3. Start all services
python start_enterprise_platform.py
```

### Option 2: Manual Setup

Follow the detailed steps below for complete control over the setup process.

## 🔧 Detailed Setup Instructions

### Step 1: Environment Preparation

#### 1.1 Check Prerequisites
```bash
# Check Python version (3.9+ required)
python --version

# Check if you're in the project directory
ls -la | grep schema.sql
```

#### 1.2 Set Up Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip
```

#### 1.3 Install Dependencies
```bash
# Install enterprise dependencies
pip install -r requirements-enterprise.txt

# Or install basic requirements if enterprise file is not available
pip install -r requirements.txt
```

### Step 2: Database Setup

#### 2.1 Install PostgreSQL
**Windows:**
- Download from https://www.postgresql.org/download/windows/
- Install with default settings
- Remember the password for 'postgres' user

**macOS:**
```bash
brew install postgresql
brew services start postgresql
```

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib
sudo systemctl start postgresql
```

#### 2.2 Create Database
```bash
# Method 1: Using automated script
python setup_database.py

# Method 2: Manual setup
createdb enterprise_support
psql enterprise_support -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";"
psql enterprise_support < schema.sql
```

### Step 3: Redis Setup

#### 3.1 Install Redis
**Windows:**
- Download from https://github.com/microsoftarchive/redis/releases
- Extract and run redis-server.exe

**macOS:**
```bash
brew install redis
brew services start redis
```

**Ubuntu/Debian:**
```bash
sudo apt-get install redis-server
sudo systemctl start redis-server
```

#### 3.2 Test Redis
```bash
redis-cli ping
# Should return: PONG
```

### Step 4: Configuration

#### 4.1 Create Environment File
Create `.env` file in the project root:
```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/enterprise_support
ORGANIZATION_ID=00000000-0000-0000-0000-000000000001

# Security
JWT_SECRET_KEY=your-super-secret-jwt-key-here-minimum-32-chars
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# Redis
REDIS_URL=redis://localhost:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Dashboard Configuration
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=8501

# Logging
LOG_LEVEL=INFO

# Optional: AI Configuration
# OPENAI_API_KEY=your-openai-api-key
```

#### 4.2 Update Database URL
Replace the DATABASE_URL with your actual PostgreSQL credentials:
```
postgresql://your_username:your_password@localhost:5432/enterprise_support
```

### Step 5: Start Services

#### 5.1 Automated Startup (Recommended)
```bash
# Start all services with monitoring
python start_enterprise_platform.py
```

#### 5.2 Manual Startup
Open 3 separate terminals:

**Terminal 1 - Redis:**
```bash
redis-server
```

**Terminal 2 - API Server:**
```bash
source venv/bin/activate  # or venv\Scripts\activate on Windows
uvicorn backend.enterprise_api:app --host 0.0.0.0 --port 8000 --reload
```

**Terminal 3 - Dashboard:**
```bash
source venv/bin/activate  # or venv\Scripts\activate on Windows
streamlit run enterprise_dashboard.py --server.port 8501
```

### Step 6: Verify Installation

#### 6.1 Check Service Health
```bash
# API Health Check
curl http://localhost:8000/health

# Dashboard Check
curl http://localhost:8501
```

#### 6.2 Access Services
- **API Server:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs
- **Enterprise Dashboard:** http://localhost:8501

#### 6.3 Test Ticket Creation
```bash
curl -X POST "http://localhost:8000/api/v2/tickets" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer mock-jwt-token" \
  -d '{
    "subject": "Test ticket from startup",
    "description": "Testing the enterprise platform startup",
    "requester_email": "<EMAIL>",
    "requester_name": "Test User",
    "priority": "medium"
  }'
```

## 🐳 Docker Alternative

If you prefer Docker deployment:

### Quick Docker Start
```bash
# Build and start all services
docker-compose -f docker-compose.enterprise.yml up -d --build

# Check status
docker-compose -f docker-compose.enterprise.yml ps

# View logs
docker-compose -f docker-compose.enterprise.yml logs -f
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Database Connection Error
```
Error: could not connect to server
```
**Solution:**
- Ensure PostgreSQL is running: `sudo systemctl status postgresql`
- Check DATABASE_URL in .env file
- Verify database exists: `psql -l | grep enterprise_support`

#### 2. Redis Connection Error
```
Error: Redis connection failed
```
**Solution:**
- Start Redis: `redis-server`
- Check Redis status: `redis-cli ping`
- Verify REDIS_URL in .env file

#### 3. Port Already in Use
```
Error: [Errno 98] Address already in use
```
**Solution:**
- Check what's using the port: `lsof -i :8000`
- Kill the process: `kill -9 <PID>`
- Or use different ports in .env file

#### 4. Module Not Found Error
```
ModuleNotFoundError: No module named 'fastapi'
```
**Solution:**
- Activate virtual environment: `source venv/bin/activate`
- Install dependencies: `pip install -r requirements-enterprise.txt`

#### 5. Permission Denied
```
PermissionError: [Errno 13] Permission denied
```
**Solution:**
- Check file permissions: `ls -la`
- Make scripts executable: `chmod +x start_platform.sh`

### Performance Issues

#### 1. Slow Database Queries
- Check PostgreSQL configuration
- Ensure indexes are created (they're in schema.sql)
- Monitor with: `SELECT * FROM pg_stat_activity;`

#### 2. High Memory Usage
- Reduce API workers in .env: `API_WORKERS=2`
- Monitor with: `htop` or `docker stats`

### Logs and Debugging

#### Check Application Logs
```bash
# Startup logs
tail -f enterprise_startup.log

# API logs
tail -f /var/log/enterprise-support/app.log

# Docker logs
docker-compose logs -f api
```

#### Enable Debug Mode
Add to .env file:
```
LOG_LEVEL=DEBUG
DEBUG=true
```

## 📊 Service Monitoring

### Health Checks
```bash
# API Health
curl http://localhost:8000/health

# System Status
curl http://localhost:8000/api/v2/status

# Database Connection
psql enterprise_support -c "SELECT COUNT(*) FROM organizations;"
```

### Performance Monitoring
- **API Metrics:** http://localhost:8000/metrics
- **Dashboard Analytics:** http://localhost:8501
- **Database Stats:** `SELECT * FROM pg_stat_database;`

## 🎯 Next Steps

Once all services are running:

1. **Configure Organization Settings**
   - Access dashboard at http://localhost:8501
   - Update organization details
   - Configure service categories

2. **Set Up Automation Rules**
   - Create auto-assignment rules
   - Configure SLA policies
   - Set up escalation workflows

3. **Add Users and Agents**
   - Import user data
   - Assign roles and permissions
   - Configure agent groups

4. **Customize Dashboard**
   - Configure KPI widgets
   - Set up custom reports
   - Enable notifications

5. **Production Deployment**
   - Set up SSL certificates
   - Configure load balancing
   - Enable monitoring and alerting

## 📞 Support

If you encounter issues:

1. **Check the logs first:** `tail -f enterprise_startup.log`
2. **Verify configuration:** Review .env file settings
3. **Test individual components:** Start services one by one
4. **Check system resources:** Ensure adequate RAM and disk space
5. **Review prerequisites:** Confirm all dependencies are installed

For additional help, refer to:
- `ENTERPRISE_IMPROVEMENTS_GUIDE.md` - Feature documentation
- `ENTERPRISE_DEPLOYMENT.md` - Production deployment
- `ENTERPRISE_TRANSFORMATION_SUMMARY.md` - Overview and benefits

---

🎉 **Congratulations!** Your enterprise technical support platform is now running and ready to transform your IT support operations!
