#!/bin/bash

echo "========================================"
echo "Enterprise Technical Support Platform"
echo "========================================"
echo

echo "Checking environment..."
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

echo "Activating virtual environment..."
source venv/bin/activate

echo "Checking basic requirements..."
python -c "import fastapi" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "FastAPI not found. Trying offline installation..."
    python install_offline.py
fi

echo "Starting Enterprise Platform (Simple Mode)..."
python start_simple_platform.py

echo
echo "Platform stopped."
