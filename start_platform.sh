#!/bin/bash

echo "========================================"
echo "Enterprise Technical Support Platform"
echo "========================================"
echo

echo "Checking environment..."
if [ ! -d "venv" ]; then
    echo "Virtual environment not found. Running setup..."
    python3 setup_enterprise_environment.py
    if [ $? -ne 0 ]; then
        echo "Setup failed. Please check the error messages above."
        exit 1
    fi
fi

echo "Activating virtual environment..."
source venv/bin/activate

echo "Checking database driver..."
python -c "import psycopg2" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing database driver..."
    pip install psycopg2-binary
fi

echo "Starting Enterprise Platform..."
python start_enterprise_platform.py

echo
echo "Platform stopped."
