import asyncio
import logging
import os
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from sentence_transformers import SentenceTransformer
from pymilvus import connections, Collection, utility
import httpx
import json
import uuid
from datetime import datetime
import hashlib
from sqlalchemy.orm import Session

from backend.database import SessionLocal
from backend.models import DocumentSource, ChatResponse

logger = logging.getLogger(__name__)

class RAGPipeline:
    def __init__(self):
        self.embedding_model = None
        self.milvus_collection = None
        self.llm_endpoint = os.getenv("LLM_ENDPOINT", "http://localhost:5000/v1/chat/completions")
        self.collection_name = "knowledge_base"
        self.embedding_dimension = 384  # all-MiniLM-L6-v2 dimension
        
    async def initialize(self):
        try:
            # Initialize embedding model
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Embedding model loaded successfully")
            
            # Connect to Milvus
            connections.connect("default", host="localhost", port="19530")
            
            # Create or load collection
            await self._setup_milvus_collection()
            
            logger.info("RAG Pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing RAG pipeline: {e}")
            raise
    
    async def _setup_milvus_collection(self):
        from pymilvus import FieldSchema, CollectionSchema, DataType
        
        if utility.has_collection(self.collection_name):
            self.milvus_collection = Collection(self.collection_name)
            logger.info(f"Loaded existing collection: {self.collection_name}")
        else:
            # Define collection schema
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="chunk_id", dtype=DataType.VARCHAR, max_length=64),
                FieldSchema(name="document_id", dtype=DataType.INT64),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=self.embedding_dimension),
                FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=255),
                FieldSchema(name="source_path", dtype=DataType.VARCHAR, max_length=500),
                FieldSchema(name="metadata", dtype=DataType.VARCHAR, max_length=2000)
            ]
            
            schema = CollectionSchema(fields, "Technical support knowledge base")
            self.milvus_collection = Collection(self.collection_name, schema)
            
            # Create index
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }
            self.milvus_collection.create_index("embedding", index_params)
            logger.info(f"Created new collection: {self.collection_name}")
        
        self.milvus_collection.load()
    
    async def generate_response(
        self,
        query: str,
        session_id: Optional[str] = None,
        user_context: Dict[str, Any] = {},
        db: SessionLocal = None
    ) -> ChatResponse:
        try:
            if not session_id:
                session_id = str(uuid.uuid4())
            
            # Retrieve relevant documents
            retrieved_docs = await self._retrieve_documents(query, top_k=5)
            
            # Generate response using LLM
            response_text, confidence_score = await self._generate_llm_response(
                query, retrieved_docs, user_context
            )
            
            # Generate suggestions
            suggestions = await self._generate_suggestions(query, retrieved_docs)
            
            # Check if escalation is needed
            needs_escalation = await self._needs_escalation(query, response_text, confidence_score)
            
            return ChatResponse(
                message=response_text,
                session_id=session_id,
                sources=retrieved_docs,
                confidence_score=confidence_score,
                response_time_ms=0,  # Will be set by caller
                suggestions=suggestions,
                needs_escalation=needs_escalation
            )
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return ChatResponse(
                message="I apologize, but I'm having trouble processing your request right now. Please try again or contact support for assistance.",
                session_id=session_id or str(uuid.uuid4()),
                sources=[],
                confidence_score=0.0,
                response_time_ms=0,
                suggestions=["Try rephrasing your question", "Contact support directly"],
                needs_escalation=True
            )
    
    async def _retrieve_documents(self, query: str, top_k: int = 5) -> List[DocumentSource]:
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query])[0].tolist()
            
            # Search in Milvus
            search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
            results = self.milvus_collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                output_fields=["chunk_id", "content", "title", "source_path"]
            )
            
            # Convert results to DocumentSource objects
            retrieved_docs = []
            for result in results[0]:
                doc = DocumentSource(
                    title=result.entity.get("title", "Unknown"),
                    content=result.entity.get("content", ""),
                    source_path=result.entity.get("source_path", ""),
                    relevance_score=float(result.score),
                    chunk_id=result.entity.get("chunk_id", "")
                )
                retrieved_docs.append(doc)
            
            return retrieved_docs
            
        except Exception as e:
            logger.error(f"Error retrieving documents: {e}")
            return []
    
    async def _generate_llm_response(
        self,
        query: str,
        retrieved_docs: List[DocumentSource],
        user_context: Dict[str, Any]
    ) -> Tuple[str, float]:
        try:
            # Build context from retrieved documents
            context = "\n\n".join([
                f"Document: {doc.title}\nContent: {doc.content}"
                for doc in retrieved_docs[:3]  # Use top 3 most relevant
            ])
            
            # Create prompt
            prompt = self._build_prompt(query, context, user_context)
            
            # Make API call to LLM
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.llm_endpoint,
                    json={
                        "model": "gpt-3.5-turbo",  # Adjust based on your LLM setup
                        "messages": [
                            {"role": "system", "content": self._get_system_prompt()},
                            {"role": "user", "content": prompt}
                        ],
                        "max_tokens": 1000,
                        "temperature": 0.3
                    },
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    response_text = result["choices"][0]["message"]["content"]
                    
                    # Calculate confidence score based on response quality
                    confidence_score = self._calculate_confidence_score(
                        query, response_text, retrieved_docs
                    )
                    
                    return response_text, confidence_score
                else:
                    logger.error(f"LLM API error: {response.status_code} - {response.text}")
                    return self._get_fallback_response(query), 0.3
                    
        except Exception as e:
            logger.error(f"Error generating LLM response: {e}")
            return self._get_fallback_response(query), 0.2
    
    def _build_prompt(self, query: str, context: str, user_context: Dict[str, Any]) -> str:
        user_info = f"User: {user_context.get('display_name', 'User')} ({user_context.get('department', 'Unknown Department')})"
        
        return f"""
{user_info}

Query: {query}

Relevant Documentation:
{context}

Please provide a helpful, accurate response based on the documentation above. If the documentation doesn't contain enough information to fully answer the question, acknowledge this and suggest next steps.
"""
    
    def _get_system_prompt(self) -> str:
        return """You are a helpful technical support assistant. Your role is to:

1. Provide accurate, step-by-step solutions to technical problems
2. Use the provided documentation to answer questions
3. Be concise but thorough in your explanations
4. If you don't have enough information, clearly state this and suggest escalation
5. Always be professional and empathetic
6. Prioritize security and best practices in your recommendations

Format your responses clearly with numbered steps when appropriate, and always include relevant warnings or prerequisites."""
    
    def _get_fallback_response(self, query: str) -> str:
        return f"I understand you're asking about '{query}'. I'm currently unable to provide a specific answer, but I'd be happy to help you get connected with a technical support specialist who can assist you further. Would you like me to escalate this issue?"
    
    def _calculate_confidence_score(
        self,
        query: str,
        response: str,
        retrieved_docs: List[DocumentSource]
    ) -> float:
        # Simple confidence scoring based on:
        # 1. Quality of retrieved documents (relevance scores)
        # 2. Response length and structure
        # 3. Presence of specific technical terms
        
        if not retrieved_docs:
            return 0.2
        
        # Average relevance score of top documents
        avg_relevance = sum(doc.relevance_score for doc in retrieved_docs[:3]) / min(3, len(retrieved_docs))
        
        # Response quality indicators
        response_length_score = min(len(response) / 200, 1.0)  # Normalize to 0-1
        has_steps = 1.0 if any(word in response.lower() for word in ['step', 'first', 'then', 'next']) else 0.5
        
        # Combine scores
        confidence = (avg_relevance * 0.5 + response_length_score * 0.3 + has_steps * 0.2)
        return min(confidence, 0.95)  # Cap at 95%
    
    async def _generate_suggestions(self, query: str, retrieved_docs: List[DocumentSource]) -> List[str]:
        suggestions = []
        
        # Generate suggestions based on common patterns
        if "password" in query.lower():
            suggestions.extend([
                "Reset your password through the self-service portal",
                "Check password complexity requirements"
            ])
        elif "email" in query.lower():
            suggestions.extend([
                "Check your email client settings",
                "Verify your network connection"
            ])
        elif "software" in query.lower():
            suggestions.extend([
                "Try restarting the application",
                "Check for software updates"
            ])
        
        # Add document-based suggestions
        for doc in retrieved_docs[:2]:
            if len(doc.title) < 100:
                suggestions.append(f"See: {doc.title}")
        
        return suggestions[:4]  # Limit to 4 suggestions
    
    async def _needs_escalation(self, query: str, response: str, confidence_score: float) -> bool:
        # Escalation criteria
        if confidence_score < 0.3:
            return True
        
        escalation_keywords = [
            "don't know", "not sure", "unable to", "can't help",
            "escalate", "specialist", "expert", "urgent", "critical"
        ]
        
        return any(keyword in response.lower() for keyword in escalation_keywords)
    
    async def log_interaction(
        self,
        session_id: str,
        user_message: str,
        assistant_response: str,
        response_time_ms: int,
        retrieved_documents: List[DocumentSource],
        user_id: int,
        db: Session
    ):
        try:
            from sqlalchemy import text
            
            # Get or create chat session
            session_query = text("""
                INSERT INTO chat_sessions (session_id, user_id, title, created_at, updated_at)
                VALUES (:session_id, :user_id, :title, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (session_id) DO UPDATE SET updated_at = CURRENT_TIMESTAMP
                RETURNING id
            """)
            
            title = user_message[:100] + "..." if len(user_message) > 100 else user_message
            session_result = db.execute(session_query, {
                "session_id": session_id,
                "user_id": user_id,
                "title": title
            })
            session_db_id = session_result.fetchone()[0]
            
            # Insert user message
            user_msg_query = text("""
                INSERT INTO chat_messages (session_id, role, content, created_at, token_count)
                VALUES (:session_id, 'user', :content, CURRENT_TIMESTAMP, :token_count)
            """)
            db.execute(user_msg_query, {
                "session_id": session_db_id,
                "content": user_message,
                "token_count": len(user_message.split())
            })
            
            # Insert assistant message with metadata
            metadata = {
                "retrieved_documents": [
                    {
                        "title": doc.title,
                        "source_path": doc.source_path,
                        "relevance_score": doc.relevance_score,
                        "chunk_id": doc.chunk_id
                    } for doc in retrieved_documents
                ]
            }
            
            assistant_msg_query = text("""
                INSERT INTO chat_messages (session_id, role, content, metadata, created_at, response_time_ms, token_count)
                VALUES (:session_id, 'assistant', :content, :metadata, CURRENT_TIMESTAMP, :response_time_ms, :token_count)
            """)
            db.execute(assistant_msg_query, {
                "session_id": session_db_id,
                "content": assistant_response,
                "metadata": json.dumps(metadata),
                "response_time_ms": response_time_ms,
                "token_count": len(assistant_response.split())
            })
            
            db.commit()
            logger.info(f"Logged interaction for session {session_id}")
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error logging interaction: {e}")
    
    async def store_feedback(
        self,
        message_id: str,
        user_id: int,
        rating: Optional[int],
        feedback_type: str,
        comment: Optional[str],
        db: Session
    ):
        try:
            from sqlalchemy import text
            
            # Get message ID from database
            msg_query = text("SELECT id FROM chat_messages WHERE message_id = :message_id")
            msg_result = db.execute(msg_query, {"message_id": message_id})
            msg_row = msg_result.fetchone()
            
            if not msg_row:
                logger.error(f"Message not found: {message_id}")
                return
            
            msg_db_id = msg_row[0]
            
            # Insert feedback
            feedback_query = text("""
                INSERT INTO user_feedback (message_id, user_id, rating, feedback_type, comment, created_at)
                VALUES (:message_id, :user_id, :rating, :feedback_type, :comment, CURRENT_TIMESTAMP)
            """)
            
            db.execute(feedback_query, {
                "message_id": msg_db_id,
                "user_id": user_id,
                "rating": rating,
                "feedback_type": feedback_type,
                "comment": comment
            })
            
            db.commit()
            logger.info(f"Stored feedback for message {message_id}")
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error storing feedback: {e}")
    
    async def create_escalation(
        self,
        session_id: str,
        user_id: int,
        issue_summary: str,
        priority: str,
        category: Optional[str],
        db: SessionLocal
    ) -> str:
        try:
            escalation_id = str(uuid.uuid4())
            # Implementation for creating escalation records
            return escalation_id
        except Exception as e:
            logger.error(f"Error creating escalation: {e}")
            raise
    
    async def get_chat_history(
        self,
        session_id: str,
        user_id: int,
        db: SessionLocal
    ) -> List[Dict[str, Any]]:
        try:
            # Implementation for retrieving chat history
            return []
        except Exception as e:
            logger.error(f"Error retrieving chat history: {e}")
            return []
    
    async def get_analytics_data(self, db: SessionLocal) -> Dict[str, Any]:
        try:
            # Implementation for analytics data
            return {
                "total_sessions": 0,
                "avg_response_time": 0,
                "satisfaction_score": 0,
                "escalation_rate": 0
            }
        except Exception as e:
            logger.error(f"Error retrieving analytics: {e}")
            return {}
    
    async def cleanup(self):
        try:
            if self.milvus_collection:
                connections.disconnect("default")
            logger.info("RAG Pipeline cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
