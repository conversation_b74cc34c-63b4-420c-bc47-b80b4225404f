"""
Metrics collection and monitoring for SharePoint AI Assistant
Provides Prometheus-compatible metrics and custom analytics
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from fastapi import APIRouter, Response, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text, func
import asyncio
import os

from .database import get_db
from .azure_auth import get_current_user, require_permission, Permissions, UserInfo

logger = logging.getLogger(__name__)

# Prometheus Metrics
# API Metrics
http_requests_total = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

http_request_duration_seconds = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

# AI Assistant Metrics
user_queries_total = Counter(
    'user_queries_total',
    'Total user queries processed',
    ['department', 'category']
)

user_escalations_total = Counter(
    'user_escalations_total',
    'Total user escalations to human support',
    ['reason', 'department']
)

ai_response_confidence = Histogram(
    'ai_response_confidence',
    'AI response confidence scores',
    ['category'],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)

staff_analyses_total = Counter(
    'staff_analyses_total',
    'Total staff ticket analyses',
    ['staff_id', 'analysis_depth']
)

ticket_resolution_time_seconds = Histogram(
    'ticket_resolution_time_seconds',
    'Time to resolve tickets in seconds',
    ['category', 'priority'],
    buckets=[300, 600, 1800, 3600, 7200, 14400, 28800, 86400]
)

# Freshservice Integration Metrics
freshservice_api_requests_total = Counter(
    'freshservice_api_requests_total',
    'Total Freshservice API requests',
    ['endpoint', 'status']
)

freshservice_api_duration_seconds = Histogram(
    'freshservice_api_duration_seconds',
    'Freshservice API request duration',
    ['endpoint']
)

freshservice_ticket_creation_failures_total = Counter(
    'freshservice_ticket_creation_failures_total',
    'Failed ticket creations in Freshservice',
    ['error_type']
)

freshservice_sync_lag_seconds = Gauge(
    'freshservice_sync_lag_seconds',
    'Lag between local and Freshservice data'
)

# System Health Metrics
azure_ad_health_check = Gauge(
    'azure_ad_health_check',
    'Azure AD authentication service health (1=healthy, 0=unhealthy)'
)

freshservice_api_up = Gauge(
    'freshservice_api_up',
    'Freshservice API availability (1=up, 0=down)'
)

database_connections_active = Gauge(
    'database_connections_active',
    'Active database connections'
)

redis_connections_active = Gauge(
    'redis_connections_active',
    'Active Redis connections'
)

# User Experience Metrics
user_satisfaction_rating = Histogram(
    'user_satisfaction_rating',
    'User satisfaction ratings',
    ['category'],
    buckets=[1, 2, 3, 4, 5]
)

user_session_duration_seconds = Histogram(
    'user_session_duration_seconds',
    'User session duration in seconds',
    buckets=[60, 300, 600, 1800, 3600, 7200]
)

user_session_timeouts_total = Counter(
    'user_session_timeouts_total',
    'Total user session timeouts',
    ['reason']
)

# Knowledge Base Metrics
knowledge_base_queries_total = Counter(
    'knowledge_base_queries_total',
    'Total knowledge base queries',
    ['category', 'result_type']
)

knowledge_base_updates_total = Counter(
    'knowledge_base_updates_total',
    'Total knowledge base updates',
    ['source', 'category']
)

class MetricsCollector:
    """Collects and manages application metrics"""
    
    def __init__(self):
        self.start_time = time.time()
        self._last_collection = None
        
    async def collect_system_metrics(self, db: Session):
        """Collect system-level metrics"""
        
        try:
            # Database connection count
            db_conn_query = text("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
            db_connections = db.execute(db_conn_query).scalar()
            database_connections_active.set(db_connections)
            
            # Check Azure AD health
            from .azure_auth import auth_health_check
            azure_health = await auth_health_check()
            azure_ad_health_check.set(1 if azure_health["status"] == "healthy" else 0)
            
            # Check Freshservice API health
            freshservice_health = await self._check_freshservice_health()
            freshservice_api_up.set(1 if freshservice_health else 0)
            
            # Calculate sync lag
            sync_lag = await self._calculate_sync_lag(db)
            freshservice_sync_lag_seconds.set(sync_lag)
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    async def collect_user_metrics(self, db: Session):
        """Collect user experience metrics"""
        
        try:
            # Recent user satisfaction
            satisfaction_query = text("""
                SELECT AVG(rating) as avg_rating, category
                FROM user_feedback uf
                JOIN ai_response_log arl ON uf.session_id = arl.session_id
                WHERE uf.created_at > NOW() - INTERVAL '1 hour'
                GROUP BY arl.suggested_category
            """)
            
            satisfaction_results = db.execute(satisfaction_query).fetchall()
            for row in satisfaction_results:
                if row.avg_rating:
                    user_satisfaction_rating.labels(category=row.category or 'unknown').observe(row.avg_rating)
            
            # Session metrics
            session_query = text("""
                SELECT 
                    EXTRACT(EPOCH FROM (ended_at - started_at)) as duration,
                    CASE WHEN ended_at IS NULL THEN 'timeout' ELSE 'normal' END as end_reason
                FROM user_sessions 
                WHERE started_at > NOW() - INTERVAL '1 hour'
                AND started_at IS NOT NULL
            """)
            
            session_results = db.execute(session_query).fetchall()
            for row in session_results:
                if row.duration:
                    user_session_duration_seconds.observe(row.duration)
                if row.end_reason == 'timeout':
                    user_session_timeouts_total.labels(reason='timeout').inc()
                    
        except Exception as e:
            logger.error(f"Error collecting user metrics: {e}")
    
    async def collect_ai_metrics(self, db: Session):
        """Collect AI-specific metrics"""
        
        try:
            # AI confidence scores from last hour
            confidence_query = text("""
                SELECT confidence_score, suggested_category
                FROM ai_response_log
                WHERE created_at > NOW() - INTERVAL '1 hour'
                AND confidence_score IS NOT NULL
            """)
            
            confidence_results = db.execute(confidence_query).fetchall()
            for row in confidence_results:
                ai_response_confidence.labels(category=row.suggested_category or 'unknown').observe(row.confidence_score)
            
            # Escalation metrics
            escalation_query = text("""
                SELECT escalation_reason, COUNT(*) as count
                FROM escalation_log
                WHERE created_at > NOW() - INTERVAL '1 hour'
                GROUP BY escalation_reason
            """)
            
            escalation_results = db.execute(escalation_query).fetchall()
            for row in escalation_results:
                # Note: Counter metrics are cumulative, so we don't set them directly
                # This is for monitoring recent trends
                pass
                
        except Exception as e:
            logger.error(f"Error collecting AI metrics: {e}")
    
    async def _check_freshservice_health(self) -> bool:
        """Check if Freshservice API is responding"""
        
        try:
            from .freshworks_integration import FreshserviceConfig, FreshserviceIntegration
            
            config = FreshserviceConfig()
            async with FreshserviceIntegration(config) as fs:
                # Simple health check - try to get agent list
                await fs._make_request("GET", "/agents", params={"per_page": 1})
                return True
                
        except Exception as e:
            logger.warning(f"Freshservice health check failed: {e}")
            return False
    
    async def _calculate_sync_lag(self, db: Session) -> float:
        """Calculate synchronization lag with Freshservice"""
        
        try:
            # Get the oldest unsynced ticket
            lag_query = text("""
                SELECT EXTRACT(EPOCH FROM (NOW() - created_date)) as lag_seconds
                FROM test_tickets
                WHERE external_ticket_id IS NULL
                AND created_date IS NOT NULL
                ORDER BY created_date ASC
                LIMIT 1
            """)
            
            result = db.execute(lag_query).fetchone()
            return result.lag_seconds if result and result.lag_seconds else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating sync lag: {e}")
            return 0.0

# Global metrics collector
metrics_collector = MetricsCollector()

# Metrics router
metrics_router = APIRouter(prefix="/metrics", tags=["Metrics"])

@metrics_router.get("/prometheus")
async def get_prometheus_metrics(
    db: Session = Depends(get_db)
):
    """Get Prometheus-formatted metrics"""
    
    # Collect latest metrics
    await metrics_collector.collect_system_metrics(db)
    await metrics_collector.collect_user_metrics(db)
    await metrics_collector.collect_ai_metrics(db)
    
    # Return Prometheus format
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

@metrics_router.get("/dashboard")
async def get_dashboard_metrics(
    hours_back: int = 24,
    db: Session = Depends(get_db),
    current_user: UserInfo = Depends(require_permission(Permissions.ANALYTICS_VIEW))
):
    """Get metrics for dashboard display"""
    
    try:
        # Define time range
        start_time = datetime.utcnow() - timedelta(hours=hours_back)
        
        # User engagement metrics
        user_metrics = await _get_user_engagement_metrics(db, start_time)
        
        # AI performance metrics
        ai_metrics = await _get_ai_performance_metrics(db, start_time)
        
        # System health metrics
        system_metrics = await _get_system_health_metrics(db, start_time)
        
        # Integration metrics
        integration_metrics = await _get_integration_metrics(db, start_time)
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "period_hours": hours_back,
            "user_engagement": user_metrics,
            "ai_performance": ai_metrics,
            "system_health": system_metrics,
            "integrations": integration_metrics
        }
        
    except Exception as e:
        logger.error(f"Error getting dashboard metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve metrics")

@metrics_router.get("/health")
async def get_health_metrics(
    db: Session = Depends(get_db)
):
    """Get health check metrics"""
    
    try:
        # Collect current health metrics
        await metrics_collector.collect_system_metrics(db)
        
        health_status = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "healthy",
            "services": {
                "database": "healthy",
                "azure_ad": "healthy" if azure_ad_health_check._value._value == 1 else "unhealthy",
                "freshservice": "healthy" if freshservice_api_up._value._value == 1 else "unhealthy",
                "redis": "healthy"
            },
            "metrics": {
                "database_connections": int(database_connections_active._value._value),
                "sync_lag_seconds": float(freshservice_sync_lag_seconds._value._value),
                "uptime_seconds": int(time.time() - metrics_collector.start_time)
            }
        }
        
        # Determine overall status
        unhealthy_services = [k for k, v in health_status["services"].items() if v != "healthy"]
        if unhealthy_services:
            health_status["overall_status"] = "degraded" if len(unhealthy_services) == 1 else "unhealthy"
            health_status["unhealthy_services"] = unhealthy_services
        
        return health_status
        
    except Exception as e:
        logger.error(f"Error getting health metrics: {e}")
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "error",
            "error": str(e)
        }

# Helper functions for metrics collection
async def _get_user_engagement_metrics(db: Session, start_time: datetime) -> Dict[str, Any]:
    """Get user engagement metrics"""
    
    query = text("""
        SELECT 
            COUNT(DISTINCT user_id) as unique_users,
            COUNT(*) as total_queries,
            AVG(confidence_score) as avg_confidence,
            COUNT(CASE WHEN requires_escalation = true THEN 1 END) as escalations
        FROM ai_response_log
        WHERE created_at >= :start_time
    """)
    
    result = db.execute(query, {"start_time": start_time}).fetchone()
    
    return {
        "unique_users": result.unique_users or 0,
        "total_queries": result.total_queries or 0,
        "average_confidence": round(result.avg_confidence or 0, 3),
        "escalation_count": result.escalations or 0,
        "escalation_rate": round((result.escalations or 0) / max(result.total_queries or 1, 1), 3)
    }

async def _get_ai_performance_metrics(db: Session, start_time: datetime) -> Dict[str, Any]:
    """Get AI performance metrics"""
    
    # Confidence distribution
    confidence_query = text("""
        SELECT 
            CASE 
                WHEN confidence_score >= 0.8 THEN 'high'
                WHEN confidence_score >= 0.6 THEN 'medium'
                ELSE 'low'
            END as confidence_level,
            COUNT(*) as count
        FROM ai_response_log
        WHERE created_at >= :start_time
        AND confidence_score IS NOT NULL
        GROUP BY 1
    """)
    
    confidence_results = db.execute(confidence_query, {"start_time": start_time}).fetchall()
    confidence_distribution = {row.confidence_level: row.count for row in confidence_results}
    
    # Category performance
    category_query = text("""
        SELECT 
            suggested_category,
            COUNT(*) as query_count,
            AVG(confidence_score) as avg_confidence,
            COUNT(CASE WHEN requires_escalation = true THEN 1 END) as escalation_count
        FROM ai_response_log
        WHERE created_at >= :start_time
        AND suggested_category IS NOT NULL
        GROUP BY suggested_category
        ORDER BY query_count DESC
        LIMIT 10
    """)
    
    category_results = db.execute(category_query, {"start_time": start_time}).fetchall()
    
    return {
        "confidence_distribution": confidence_distribution,
        "category_performance": [
            {
                "category": row.suggested_category,
                "query_count": row.query_count,
                "avg_confidence": round(row.avg_confidence, 3),
                "escalation_rate": round(row.escalation_count / row.query_count, 3)
            }
            for row in category_results
        ]
    }

async def _get_system_health_metrics(db: Session, start_time: datetime) -> Dict[str, Any]:
    """Get system health metrics"""
    
    # Database metrics
    db_query = text("SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active'")
    db_connections = db.execute(db_query).scalar()
    
    return {
        "database": {
            "active_connections": db_connections,
            "status": "healthy"
        },
        "cache": {
            "status": "healthy"
        },
        "authentication": {
            "azure_ad_status": "healthy" if azure_ad_health_check._value._value == 1 else "unhealthy"
        }
    }

async def _get_integration_metrics(db: Session, start_time: datetime) -> Dict[str, Any]:
    """Get integration metrics"""
    
    # Freshservice metrics
    fs_query = text("""
        SELECT 
            COUNT(*) as total_tickets,
            COUNT(CASE WHEN external_ticket_id IS NOT NULL THEN 1 END) as synced_tickets,
            AVG(EXTRACT(EPOCH FROM (last_updated - created_date))) as avg_sync_time
        FROM test_tickets
        WHERE created_date >= :start_time
    """)
    
    fs_result = db.execute(fs_query, {"start_time": start_time}).fetchone()
    
    return {
        "freshservice": {
            "total_tickets": fs_result.total_tickets or 0,
            "synced_tickets": fs_result.synced_tickets or 0,
            "sync_rate": round((fs_result.synced_tickets or 0) / max(fs_result.total_tickets or 1, 1), 3),
            "avg_sync_time_seconds": round(fs_result.avg_sync_time or 0, 1),
            "api_status": "healthy" if freshservice_api_up._value._value == 1 else "unhealthy"
        }
    }

# Middleware to track HTTP metrics
class MetricsMiddleware:
    """Middleware to collect HTTP request metrics"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        start_time = time.time()
        method = scope["method"]
        path = scope["path"]
        
        # Normalize path for metrics
        endpoint = self._normalize_path(path)
        
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                duration = time.time() - start_time
                status = str(message["status"])
                
                # Record metrics
                http_requests_total.labels(method=method, endpoint=endpoint, status=status).inc()
                http_request_duration_seconds.labels(method=method, endpoint=endpoint).observe(duration)
            
            await send(message)
        
        await self.app(scope, receive, send_wrapper)
    
    def _normalize_path(self, path: str) -> str:
        """Normalize path for metrics grouping"""
        
        # Remove IDs and dynamic parts
        import re
        
        # Replace UUIDs and IDs with placeholders
        path = re.sub(r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', '/{uuid}', path)
        path = re.sub(r'/\d+', '/{id}', path)
        path = re.sub(r'/session_[^/]+', '/session_{id}', path)
        
        return path

# Background task to collect metrics periodically
async def collect_metrics_background(db: Session):
    """Background task to collect metrics"""
    
    while True:
        try:
            await metrics_collector.collect_system_metrics(db)
            await metrics_collector.collect_user_metrics(db)
            await metrics_collector.collect_ai_metrics(db)
            
            # Sleep for 5 minutes
            await asyncio.sleep(300)
            
        except Exception as e:
            logger.error(f"Error in background metrics collection: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error

# Function to record custom metrics
def record_user_query(department: str, category: str, confidence: float):
    """Record a user query metric"""
    user_queries_total.labels(department=department or 'unknown', category=category or 'unknown').inc()
    ai_response_confidence.labels(category=category or 'unknown').observe(confidence)

def record_escalation(reason: str, department: str):
    """Record an escalation metric"""
    user_escalations_total.labels(reason=reason or 'unknown', department=department or 'unknown').inc()

def record_staff_analysis(staff_id: str, analysis_depth: str):
    """Record a staff analysis metric"""
    staff_analyses_total.labels(staff_id=staff_id, analysis_depth=analysis_depth).inc()

def record_freshservice_request(endpoint: str, status: str, duration: float):
    """Record a Freshservice API request metric"""
    freshservice_api_requests_total.labels(endpoint=endpoint, status=status).inc()
    freshservice_api_duration_seconds.labels(endpoint=endpoint).observe(duration)

def record_ticket_resolution(category: str, priority: str, resolution_time: float):
    """Record ticket resolution time"""
    ticket_resolution_time_seconds.labels(category=category, priority=priority).observe(resolution_time)