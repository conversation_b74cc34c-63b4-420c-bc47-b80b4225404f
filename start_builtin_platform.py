#!/usr/bin/env python3
"""
Enterprise Technical Support Platform - Pure Python Implementation
Uses only built-in Python packages, no external dependencies required
"""

import os
import sys
import json
import sqlite3
import threading
import subprocess
import signal
import logging
import socket
import time
from datetime import datetime, timedelta
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('builtin_platform.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TicketManager:
    """Simple ticket management using SQLite"""
    
    def __init__(self, db_path="enterprise_support.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database with basic schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create basic tickets table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tickets (
                id TEXT PRIMARY KEY,
                ticket_number TEXT UNIQUE,
                subject TEXT NOT NULL,
                description TEXT NOT NULL,
                status TEXT DEFAULT 'open',
                priority TEXT DEFAULT 'medium',
                requester_email TEXT NOT NULL,
                requester_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create activities table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ticket_activities (
                id TEXT PRIMARY KEY,
                ticket_id TEXT,
                activity_type TEXT,
                content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES tickets (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("✅ Database initialized")
    
    def create_ticket(self, data):
        """Create a new ticket"""
        ticket_id = str(uuid.uuid4())
        ticket_number = f"TKT-{int(time.time())}"
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO tickets (id, ticket_number, subject, description, 
                               requester_email, requester_name, priority)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            ticket_id, ticket_number, data['subject'], data['description'],
            data['requester_email'], data['requester_name'], 
            data.get('priority', 'medium')
        ))
        
        # Add creation activity
        cursor.execute('''
            INSERT INTO ticket_activities (id, ticket_id, activity_type, content)
            VALUES (?, ?, ?, ?)
        ''', (str(uuid.uuid4()), ticket_id, 'created', 'Ticket created'))
        
        conn.commit()
        conn.close()
        
        return {
            'id': ticket_id,
            'ticket_number': ticket_number,
            'subject': data['subject'],
            'status': 'open',
            'created_at': datetime.now().isoformat()
        }
    
    def get_tickets(self, limit=50):
        """Get all tickets"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, ticket_number, subject, status, priority, 
                   requester_name, created_at
            FROM tickets 
            ORDER BY created_at DESC 
            LIMIT ?
        ''', (limit,))
        
        tickets = []
        for row in cursor.fetchall():
            tickets.append({
                'id': row[0],
                'ticket_number': row[1],
                'subject': row[2],
                'status': row[3],
                'priority': row[4],
                'requester_name': row[5],
                'created_at': row[6]
            })
        
        conn.close()
        return tickets
    
    def get_ticket(self, ticket_id):
        """Get a specific ticket"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM tickets WHERE id = ?
        ''', (ticket_id,))
        
        row = cursor.fetchone()
        if row:
            ticket = {
                'id': row[0],
                'ticket_number': row[1],
                'subject': row[2],
                'description': row[3],
                'status': row[4],
                'priority': row[5],
                'requester_email': row[6],
                'requester_name': row[7],
                'created_at': row[8],
                'updated_at': row[9]
            }
            
            # Get activities
            cursor.execute('''
                SELECT activity_type, content, created_at 
                FROM ticket_activities 
                WHERE ticket_id = ? 
                ORDER BY created_at
            ''', (ticket_id,))
            
            ticket['activities'] = [
                {
                    'type': row[0],
                    'content': row[1],
                    'created_at': row[2]
                }
                for row in cursor.fetchall()
            ]
            
            conn.close()
            return ticket
        
        conn.close()
        return None

class APIHandler(BaseHTTPRequestHandler):
    """Simple HTTP API handler"""
    
    def __init__(self, *args, ticket_manager=None, **kwargs):
        self.ticket_manager = ticket_manager
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        path = urlparse(self.path).path
        
        if path == '/health':
            self.send_json_response({'status': 'healthy', 'timestamp': datetime.now().isoformat()})
        elif path == '/api/tickets':
            tickets = self.ticket_manager.get_tickets()
            self.send_json_response({'tickets': tickets, 'count': len(tickets)})
        elif path.startswith('/api/tickets/'):
            ticket_id = path.split('/')[-1]
            ticket = self.ticket_manager.get_ticket(ticket_id)
            if ticket:
                self.send_json_response(ticket)
            else:
                self.send_error(404, 'Ticket not found')
        elif path == '/':
            self.send_dashboard_html()
        else:
            self.send_error(404, 'Not found')
    
    def do_POST(self):
        """Handle POST requests"""
        path = urlparse(self.path).path
        
        if path == '/api/tickets':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                ticket = self.ticket_manager.create_ticket(data)
                self.send_json_response(ticket, status=201)
            except Exception as e:
                self.send_error(400, f'Invalid request: {e}')
        else:
            self.send_error(404, 'Not found')
    
    def send_json_response(self, data, status=200):
        """Send JSON response"""
        self.send_response(status)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode('utf-8'))
    
    def send_dashboard_html(self):
        """Send simple dashboard HTML"""
        html = '''
<!DOCTYPE html>
<html>
<head>
    <title>Enterprise Support Platform</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: #1f4e79; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #1f4e79; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #2a5a8a; }
        .ticket { border-left: 4px solid #1f4e79; padding: 10px; margin: 10px 0; background: #f9f9f9; }
        .status-open { border-left-color: #28a745; }
        .status-closed { border-left-color: #6c757d; }
        .priority-high { border-left-color: #dc3545; }
        .priority-critical { border-left-color: #fd7e14; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Enterprise Technical Support Platform</h1>
        <p>Pure Python Implementation - No External Dependencies</p>
    </div>
    
    <div class="card">
        <h2>📝 Create New Ticket</h2>
        <form id="ticketForm">
            <div class="form-group">
                <label>Subject:</label>
                <input type="text" id="subject" required>
            </div>
            <div class="form-group">
                <label>Description:</label>
                <textarea id="description" rows="4" required></textarea>
            </div>
            <div class="form-group">
                <label>Priority:</label>
                <select id="priority">
                    <option value="low">Low</option>
                    <option value="medium" selected>Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                </select>
            </div>
            <div class="form-group">
                <label>Your Name:</label>
                <input type="text" id="requester_name" required>
            </div>
            <div class="form-group">
                <label>Your Email:</label>
                <input type="email" id="requester_email" required>
            </div>
            <button type="submit">Create Ticket</button>
        </form>
    </div>
    
    <div class="card">
        <h2>🎫 Recent Tickets</h2>
        <div id="ticketsList">Loading tickets...</div>
        <button onclick="loadTickets()">Refresh</button>
    </div>
    
    <script>
        // Load tickets on page load
        loadTickets();
        
        // Handle form submission
        document.getElementById('ticketForm').addEventListener('submit', function(e) {
            e.preventDefault();
            createTicket();
        });
        
        function createTicket() {
            const data = {
                subject: document.getElementById('subject').value,
                description: document.getElementById('description').value,
                priority: document.getElementById('priority').value,
                requester_name: document.getElementById('requester_name').value,
                requester_email: document.getElementById('requester_email').value
            };
            
            fetch('/api/tickets', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                alert('Ticket created: ' + result.ticket_number);
                document.getElementById('ticketForm').reset();
                loadTickets();
            })
            .catch(error => {
                alert('Error creating ticket: ' + error);
            });
        }
        
        function loadTickets() {
            fetch('/api/tickets')
            .then(response => response.json())
            .then(data => {
                const ticketsList = document.getElementById('ticketsList');
                if (data.tickets.length === 0) {
                    ticketsList.innerHTML = '<p>No tickets found.</p>';
                    return;
                }
                
                let html = '';
                data.tickets.forEach(ticket => {
                    html += `
                        <div class="ticket status-${ticket.status} priority-${ticket.priority}">
                            <strong>${ticket.ticket_number}</strong> - ${ticket.subject}
                            <br><small>By: ${ticket.requester_name} | Status: ${ticket.status} | Priority: ${ticket.priority}</small>
                            <br><small>Created: ${new Date(ticket.created_at).toLocaleString()}</small>
                        </div>
                    `;
                });
                ticketsList.innerHTML = html;
            })
            .catch(error => {
                document.getElementById('ticketsList').innerHTML = '<p>Error loading tickets: ' + error + '</p>';
            });
        }
    </script>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to reduce log noise"""
        pass

class BuiltinPlatformManager:
    """Manages the built-in platform services"""
    
    def __init__(self):
        self.ticket_manager = TicketManager()
        self.server = None
        self.server_thread = None
        self.running = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)
    
    def start(self, host='127.0.0.1', port=8000):
        """Start the platform"""
        logger.info("🚀 Starting Enterprise Platform (Built-in Mode)...")
        
        # Check if port is available
        if not self._check_port(host, port):
            logger.error(f"❌ Port {port} is already in use")
            return False
        
        try:
            # Create HTTP server with ticket manager
            def handler(*args, **kwargs):
                return APIHandler(*args, ticket_manager=self.ticket_manager, **kwargs)
            
            self.server = HTTPServer((host, port), handler)
            self.running = True
            
            # Start server in a separate thread
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            logger.info(f"✅ Platform started successfully")
            self._print_status(host, port)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start platform: {e}")
            return False
    
    def _check_port(self, host, port):
        """Check if port is available"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind((host, port))
                return True
        except OSError:
            return False
    
    def _print_status(self, host, port):
        """Print platform status"""
        print("\n" + "="*60)
        print("🎯 ENTERPRISE TECHNICAL SUPPORT PLATFORM")
        print("   (Built-in Python Implementation)")
        print("="*60)
        print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 Server: http://{host}:{port}")
        print(f"📡 API: http://{host}:{port}/api/tickets")
        print(f"💾 Database: SQLite (enterprise_support.db)")
        print("\n💡 Features Available:")
        print("  • Ticket creation and management")
        print("  • Simple web dashboard")
        print("  • REST API endpoints")
        print("  • SQLite database storage")
        print("\n🎮 Quick Commands:")
        print("  • Open dashboard: http://127.0.0.1:8000")
        print("  • Press Ctrl+C to stop")
        print("="*60)
    
    def stop(self):
        """Stop the platform"""
        if self.server:
            logger.info("🛑 Stopping platform...")
            self.running = False
            self.server.shutdown()
            self.server.server_close()
            if self.server_thread:
                self.server_thread.join(timeout=5)
            logger.info("✅ Platform stopped")
    
    def wait(self):
        """Wait for the platform to stop"""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("👋 Shutdown requested by user")
            self.stop()

def main():
    """Main function"""
    print("🎯 Enterprise Technical Support Platform")
    print("   Pure Python Implementation")
    print("=" * 50)
    
    manager = BuiltinPlatformManager()
    
    if manager.start():
        try:
            manager.wait()
        except KeyboardInterrupt:
            pass
        finally:
            manager.stop()
    else:
        logger.error("❌ Failed to start platform")
        sys.exit(1)

if __name__ == "__main__":
    main()
