"""
Microsoft Teams Bot Integration for SharePoint AI Assistant
Provides Teams-native chat support with adaptive cards and proactive messaging
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from botbuilder.core import (
    TurnContext, 
    MessageFactory, 
    ActivityHandler, 
    BotFrameworkAdapter,
    BotFrameworkAdapterSettings,
    ConversationState,
    UserState,
    MemoryStorage
)
from botbuilder.core.conversation_state import ConversationState
from botbuilder.core.user_state import UserState
from botbuilder.schema import (
    Activity, 
    ActivityTypes, 
    ChannelAccount, 
    CardAction, 
    ActionTypes,
    SuggestedActions
)
from botbuilder.core.message_factory import MessageFactory
from fastapi import APIRouter, Request, HTTPException
import httpx

from .azure_auth import get_user_from_teams_context
from .sharepoint_user_assistant import SharePointUserAssistant
from .sharepoint_staff_assistant import SharePointStaffAssistant
from .database import get_db

logger = logging.getLogger(__name__)

@dataclass
class TeamsConfig:
    """Teams Bot configuration"""
    app_id: str = os.getenv("TEAMS_BOT_APP_ID", "")
    app_password: str = os.getenv("TEAMS_BOT_APP_PASSWORD", "")
    service_url: str = os.getenv("TEAMS_SERVICE_URL", "https://smba.trafficmanager.net/amer/")
    
    def __post_init__(self):
        if not self.app_id or not self.app_password:
            raise ValueError("Teams Bot App ID and Password must be configured")

class TeamsBot(ActivityHandler):
    """Microsoft Teams Bot for AI Assistant"""
    
    def __init__(self, conversation_state: ConversationState, user_state: UserState):
        self.conversation_state = conversation_state
        self.user_state = user_state
        self.user_assistant = SharePointUserAssistant()
        self.staff_assistant = SharePointStaffAssistant()
        
    async def on_message_activity(self, turn_context: TurnContext):
        """Handle incoming messages from Teams"""
        
        try:
            # Get user information from Teams context
            user_info = await get_user_from_teams_context(turn_context)
            user_message = turn_context.activity.text
            
            logger.info(f"Teams message from {user_info.get('email', 'unknown')}: {user_message}")
            
            # Check if this is a staff member or regular user
            is_staff = user_info.get('role') in ['admin', 'agent', 'staff']
            
            if is_staff and user_message.lower().startswith('/analyze'):
                # Staff command for ticket analysis
                await self._handle_staff_analysis(turn_context, user_info, user_message)
            else:
                # Regular user query
                await self._handle_user_query(turn_context, user_info, user_message)
                
        except Exception as e:
            logger.error(f"Error handling Teams message: {e}")
            await turn_context.send_activity(
                MessageFactory.text("I'm sorry, I encountered an error processing your message. Please try again.")
            )
    
    async def _handle_user_query(self, turn_context: TurnContext, user_info: Dict, message: str):
        """Handle regular user queries"""
        
        # Send typing indicator
        await turn_context.send_activity(
            MessageFactory.text("I'm thinking... 🤔")
        )
        
        try:
            # Process query with user assistant
            with get_db() as db:
                response = await self.user_assistant.process_user_query(
                    user_id=user_info.get('id', 'teams_user'),
                    user_email=user_info.get('email', '<EMAIL>'),
                    user_name=user_info.get('name', 'Teams User'),
                    query=message,
                    department=user_info.get('department', 'Unknown'),
                    db=db
                )
            
            # Create adaptive card response
            card = self._create_response_card(response, user_info)
            card_attachment = MessageFactory.attachment(card)
            
            await turn_context.send_activity(card_attachment)
            
            # Add quick action suggestions
            if response.get('suggestions'):
                suggestions = self._create_suggestion_actions(response['suggestions'])
                await turn_context.send_activity(suggestions)
                
        except Exception as e:
            logger.error(f"Error processing user query: {e}")
            await turn_context.send_activity(
                MessageFactory.text("I'm sorry, I couldn't process your request right now. Please try again later.")
            )
    
    async def _handle_staff_analysis(self, turn_context: TurnContext, user_info: Dict, message: str):
        """Handle staff ticket analysis commands"""
        
        # Extract ticket ID from command
        parts = message.split()
        if len(parts) < 2:
            await turn_context.send_activity(
                MessageFactory.text("Please provide a ticket ID. Usage: /analyze [ticket_id]")
            )
            return
        
        ticket_id = parts[1]
        
        try:
            # Send typing indicator
            await turn_context.send_activity(
                MessageFactory.text("Analyzing ticket... 📊")
            )
            
            # Process with staff assistant
            with get_db() as db:
                analysis = await self.staff_assistant.analyze_ticket(
                    ticket_id=ticket_id,
                    staff_id=user_info.get('id', 'teams_staff'),
                    staff_email=user_info.get('email', '<EMAIL>'),
                    staff_name=user_info.get('name', 'Teams Staff'),
                    include_similar_tickets=True,
                    analysis_depth='standard',
                    db=db
                )
            
            # Create analysis card
            card = self._create_analysis_card(analysis, ticket_id)
            card_attachment = MessageFactory.attachment(card)
            
            await turn_context.send_activity(card_attachment)
            
        except Exception as e:
            logger.error(f"Error analyzing ticket: {e}")
            await turn_context.send_activity(
                MessageFactory.text(f"I couldn't analyze ticket {ticket_id}. Please check the ticket ID and try again.")
            )
    
    def _create_response_card(self, response: Dict, user_info: Dict) -> Dict:
        """Create adaptive card for AI response"""
        
        card = {
            "type": "AdaptiveCard",
            "version": "1.4",
            "body": [
                {
                    "type": "TextBlock",
                    "text": "🤖 AI Assistant Response",
                    "weight": "Bolder",
                    "size": "Medium",
                    "color": "Accent"
                },
                {
                    "type": "TextBlock",
                    "text": response.get('response', 'No response available'),
                    "wrap": True,
                    "spacing": "Medium"
                }
            ]
        }
        
        # Add confidence score if available
        if response.get('confidence_score'):
            confidence = response['confidence_score']
            confidence_color = "Good" if confidence > 0.8 else "Warning" if confidence > 0.6 else "Attention"
            
            card["body"].append({
                "type": "FactSet",
                "facts": [
                    {
                        "title": "Confidence:",
                        "value": f"{confidence:.1%}"
                    }
                ],
                "spacing": "Medium"
            })
        
        # Add sources if available
        if response.get('sources'):
            sources_text = "\n".join([f"• {source}" for source in response['sources'][:3]])
            card["body"].append({
                "type": "TextBlock",
                "text": f"**Sources:**\n{sources_text}",
                "wrap": True,
                "spacing": "Medium",
                "size": "Small"
            })
        
        # Add action buttons
        actions = []
        
        if response.get('requires_escalation'):
            actions.append({
                "type": "Action.Submit",
                "title": "🚨 Escalate to Human",
                "data": {
                    "action": "escalate",
                    "session_id": response.get('session_id')
                }
            })
        
        actions.extend([
            {
                "type": "Action.Submit", 
                "title": "👍 Helpful",
                "data": {
                    "action": "feedback",
                    "rating": "helpful",
                    "session_id": response.get('session_id')
                }
            },
            {
                "type": "Action.Submit",
                "title": "👎 Not Helpful", 
                "data": {
                    "action": "feedback",
                    "rating": "not_helpful",
                    "session_id": response.get('session_id')
                }
            }
        ])
        
        if actions:
            card["actions"] = actions
        
        return {
            "contentType": "application/vnd.microsoft.card.adaptive",
            "content": card
        }
    
    def _create_analysis_card(self, analysis: Dict, ticket_id: str) -> Dict:
        """Create adaptive card for ticket analysis"""
        
        card = {
            "type": "AdaptiveCard", 
            "version": "1.4",
            "body": [
                {
                    "type": "TextBlock",
                    "text": f"📊 Ticket Analysis: #{ticket_id}",
                    "weight": "Bolder",
                    "size": "Medium",
                    "color": "Accent"
                }
            ]
        }
        
        # Add priority and category
        if analysis.get('priority') or analysis.get('category'):
            facts = []
            if analysis.get('priority'):
                facts.append({"title": "Priority:", "value": analysis['priority']})
            if analysis.get('category'):
                facts.append({"title": "Category:", "value": analysis['category']})
            
            card["body"].append({
                "type": "FactSet",
                "facts": facts,
                "spacing": "Medium"
            })
        
        # Add recommendations
        if analysis.get('recommendations'):
            recommendations_text = "\n".join([f"• {rec}" for rec in analysis['recommendations'][:3]])
            card["body"].append({
                "type": "TextBlock",
                "text": f"**Recommendations:**\n{recommendations_text}",
                "wrap": True,
                "spacing": "Medium"
            })
        
        # Add similar tickets
        if analysis.get('similar_tickets'):
            similar_text = "\n".join([f"• #{ticket['id']} - {ticket['subject'][:50]}..." 
                                    for ticket in analysis['similar_tickets'][:2]])
            card["body"].append({
                "type": "TextBlock",
                "text": f"**Similar Tickets:**\n{similar_text}",
                "wrap": True,
                "spacing": "Medium",
                "size": "Small"
            })
        
        # Add action buttons
        card["actions"] = [
            {
                "type": "Action.Submit",
                "title": "📝 Add Note",
                "data": {
                    "action": "add_note",
                    "ticket_id": ticket_id
                }
            },
            {
                "type": "Action.Submit", 
                "title": "✅ Mark Resolved",
                "data": {
                    "action": "resolve_ticket",
                    "ticket_id": ticket_id
                }
            },
            {
                "type": "Action.Submit",
                "title": "📋 View Full Ticket",
                "data": {
                    "action": "view_ticket",
                    "ticket_id": ticket_id
                }
            }
        ]
        
        return {
            "contentType": "application/vnd.microsoft.card.adaptive",
            "content": card
        }
    
    def _create_suggestion_actions(self, suggestions: List[str]) -> Activity:
        """Create suggested actions for quick responses"""
        
        suggested_actions = SuggestedActions(
            actions=[
                CardAction(
                    title=suggestion[:20] + "..." if len(suggestion) > 20 else suggestion,
                    type=ActionTypes.im_back,
                    value=suggestion
                )
                for suggestion in suggestions[:3]  # Limit to 3 suggestions
            ]
        )
        
        return MessageFactory.text(
            "Here are some related topics you might find helpful:",
            suggested_actions=suggested_actions
        )
    
    async def on_activity(self, turn_context: TurnContext):
        """Handle card actions and other activities"""
        
        if turn_context.activity.type == ActivityTypes.message:
            if hasattr(turn_context.activity, 'value') and turn_context.activity.value:
                # Handle card action
                await self._handle_card_action(turn_context)
            else:
                # Handle regular message
                await super().on_activity(turn_context)
        else:
            await super().on_activity(turn_context)
    
    async def _handle_card_action(self, turn_context: TurnContext):
        """Handle adaptive card action submissions"""
        
        action_data = turn_context.activity.value
        action = action_data.get('action')
        
        try:
            if action == 'feedback':
                await self._handle_feedback_action(turn_context, action_data)
            elif action == 'escalate':
                await self._handle_escalation_action(turn_context, action_data)
            elif action == 'add_note':
                await self._handle_add_note_action(turn_context, action_data)
            elif action == 'resolve_ticket':
                await self._handle_resolve_ticket_action(turn_context, action_data)
            elif action == 'view_ticket':
                await self._handle_view_ticket_action(turn_context, action_data)
            else:
                await turn_context.send_activity(
                    MessageFactory.text("Unknown action. Please try again.")
                )
                
        except Exception as e:
            logger.error(f"Error handling card action: {e}")
            await turn_context.send_activity(
                MessageFactory.text("I encountered an error processing your action. Please try again.")
            )
    
    async def _handle_feedback_action(self, turn_context: TurnContext, action_data: Dict):
        """Handle feedback submission"""
        
        rating = action_data.get('rating')
        session_id = action_data.get('session_id')
        
        # Store feedback in database
        with get_db() as db:
            await self.user_assistant.store_feedback(
                session_id=session_id,
                rating=5 if rating == 'helpful' else 2,
                feedback_type=rating,
                comment=f"Teams feedback: {rating}",
                db=db
            )
        
        await turn_context.send_activity(
            MessageFactory.text("✅ Thank you for your feedback! It helps me improve.")
        )
    
    async def _handle_escalation_action(self, turn_context: TurnContext, action_data: Dict):
        """Handle escalation to human support"""
        
        session_id = action_data.get('session_id')
        user_info = await get_user_from_teams_context(turn_context)
        
        # Create escalation
        with get_db() as db:
            escalation_id = await self.user_assistant.escalate_to_human(
                session_id=session_id,
                user_id=user_info.get('id'),
                reason="User requested human assistance from Teams",
                db=db
            )
        
        await turn_context.send_activity(
            MessageFactory.text(f"🚨 Your issue has been escalated to human support. Escalation ID: {escalation_id}")
        )
    
    async def _handle_add_note_action(self, turn_context: TurnContext, action_data: Dict):
        """Handle adding note to ticket"""
        
        ticket_id = action_data.get('ticket_id')
        
        await turn_context.send_activity(
            MessageFactory.text(f"Please type your note for ticket #{ticket_id}:")
        )
        
        # Store context for next message
        # Implementation would depend on conversation state management
    
    async def _handle_resolve_ticket_action(self, turn_context: TurnContext, action_data: Dict):
        """Handle marking ticket as resolved"""
        
        ticket_id = action_data.get('ticket_id')
        user_info = await get_user_from_teams_context(turn_context)
        
        # Update ticket status
        with get_db() as db:
            success = await self.staff_assistant.update_ticket_status(
                ticket_id=ticket_id,
                status='resolved',
                staff_id=user_info.get('id'),
                db=db
            )
        
        if success:
            await turn_context.send_activity(
                MessageFactory.text(f"✅ Ticket #{ticket_id} has been marked as resolved.")
            )
        else:
            await turn_context.send_activity(
                MessageFactory.text(f"❌ Failed to update ticket #{ticket_id}. Please try again.")
            )
    
    async def _handle_view_ticket_action(self, turn_context: TurnContext, action_data: Dict):
        """Handle viewing full ticket details"""
        
        ticket_id = action_data.get('ticket_id')
        
        # Create URL to ticket in SharePoint or Freshservice
        ticket_url = f"https://your-sharepoint-site.com/tickets/{ticket_id}"
        
        card = {
            "type": "AdaptiveCard",
            "version": "1.4", 
            "body": [
                {
                    "type": "TextBlock",
                    "text": f"🎫 Ticket #{ticket_id}",
                    "weight": "Bolder",
                    "size": "Medium"
                },
                {
                    "type": "TextBlock",
                    "text": "Click below to view the full ticket details in your browser.",
                    "wrap": True
                }
            ],
            "actions": [
                {
                    "type": "Action.OpenUrl",
                    "title": "Open Ticket",
                    "url": ticket_url
                }
            ]
        }
        
        card_attachment = MessageFactory.attachment({
            "contentType": "application/vnd.microsoft.card.adaptive",
            "content": card
        })
        
        await turn_context.send_activity(card_attachment)

class TeamsIntegration:
    """Teams integration manager"""
    
    def __init__(self):
        self.config = TeamsConfig()
        
        # Bot Framework settings
        self.settings = BotFrameworkAdapterSettings(
            app_id=self.config.app_id,
            app_password=self.config.app_password
        )
        
        # Create adapter and bot
        self.adapter = BotFrameworkAdapter(self.settings)
        
        # Create storage and state
        memory_storage = MemoryStorage()
        self.conversation_state = ConversationState(memory_storage)
        self.user_state = UserState(memory_storage)
        
        # Create bot
        self.bot = TeamsBot(self.conversation_state, self.user_state)
        
        # Error handler
        async def on_error(context: TurnContext, error: Exception):
            logger.error(f"Teams bot error: {error}")
            await context.send_activity(
                MessageFactory.text("Sorry, I encountered an error. Please try again.")
            )
        
        self.adapter.on_turn_error = on_error
    
    async def process_request(self, request_body: str, auth_header: str = None) -> Dict:
        """Process incoming Teams request"""
        
        try:
            activity = Activity().deserialize(json.loads(request_body))
            auth_header = auth_header or ""
            
            # Process the activity
            response = await self.adapter.process_activity(activity, auth_header, self.bot.on_activity)
            
            return {"status": "success", "response": response}
            
        except Exception as e:
            logger.error(f"Error processing Teams request: {e}")
            return {"status": "error", "message": str(e)}
    
    async def send_proactive_message(self, user_id: str, message: str, card: Dict = None):
        """Send proactive message to Teams user"""
        
        try:
            # Implementation for proactive messaging
            # This requires storing conversation references when users first interact
            logger.info(f"Proactive message to {user_id}: {message}")
            return {"status": "success"}
            
        except Exception as e:
            logger.error(f"Error sending proactive message: {e}")
            return {"status": "error", "message": str(e)}

# FastAPI router for Teams webhook
teams_router = APIRouter(prefix="/api/v1/teams", tags=["Teams Integration"])

# Global Teams integration instance
teams_integration = TeamsIntegration()

@teams_router.post("/webhook")
async def teams_webhook(request: Request):
    """Teams bot webhook endpoint"""
    
    try:
        body = await request.body()
        auth_header = request.headers.get("Authorization", "")
        
        result = await teams_integration.process_request(body.decode(), auth_header)
        
        return result
        
    except Exception as e:
        logger.error(f"Teams webhook error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@teams_router.post("/notify/{user_id}")
async def send_teams_notification(user_id: str, message: str, card: Dict = None):
    """Send proactive notification to Teams user"""
    
    result = await teams_integration.send_proactive_message(user_id, message, card)
    return result