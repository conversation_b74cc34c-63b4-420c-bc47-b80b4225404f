from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uvicorn
import os
import json
from datetime import datetime, timedelta
import logging
import uuid

from backend.database import get_db, SessionLocal
from backend.rag_pipeline import RAGPipeline
from backend.models import ChatRequest, ChatResponse, UserFeedback, EscalationRequest
from backend.auth import verify_token, get_current_user
from backend.freshworks_integration import (
    FreshserviceIntegration, 
    FreshserviceConfig,
    create_ticket_from_escalation,
    update_ticket_from_chat_feedback,
    FreshserviceAPIError
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Technical Support Chatbot API",
    description="AI-powered technical support assistant with RAG capabilities",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

security = HTTPBearer()
rag_pipeline = RAGPipeline()

@app.on_event("startup")
async def startup_event():
    logger.info("Starting Technical Support Chatbot API")
    await rag_pipeline.initialize()

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down Technical Support Chatbot API")
    await rag_pipeline.cleanup()

@app.get("/")
async def root():
    return {"message": "Technical Support Chatbot API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "database": "healthy",
            "vector_db": "healthy",
            "llm": "healthy"
        }
    }

@app.post("/api/v1/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    current_user: dict = Depends(get_current_user),
    db: SessionLocal = Depends(get_db)
):
    try:
        start_time = datetime.utcnow()
        
        # Generate response using RAG pipeline
        response = await rag_pipeline.generate_response(
            query=request.message,
            session_id=request.session_id,
            user_context=current_user,
            db=db
        )
        
        # Calculate response time
        response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        # Log the interaction
        await rag_pipeline.log_interaction(
            session_id=request.session_id,
            user_message=request.message,
            assistant_response=response.message,
            response_time_ms=int(response_time),
            retrieved_documents=response.sources,
            user_id=current_user["id"],
            db=db
        )
        
        return ChatResponse(
            message=response.message,
            session_id=response.session_id,
            sources=response.sources,
            confidence_score=response.confidence_score,
            response_time_ms=int(response_time),
            suggestions=response.suggestions
        )
        
    except Exception as e:
        logger.error(f"Error processing chat request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing chat request: {str(e)}"
        )

@app.post("/api/v1/feedback")
async def submit_feedback(
    feedback: UserFeedback,
    current_user: dict = Depends(get_current_user),
    db: SessionLocal = Depends(get_db)
):
    try:
        await rag_pipeline.store_feedback(
            message_id=feedback.message_id,
            user_id=current_user["id"],
            rating=feedback.rating,
            feedback_type=feedback.feedback_type,
            comment=feedback.comment,
            db=db
        )
        
        return {"status": "success", "message": "Feedback submitted successfully"}
        
    except Exception as e:
        logger.error(f"Error submitting feedback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error submitting feedback: {str(e)}"
        )

@app.post("/api/v1/escalate")
async def escalate_issue(
    escalation: EscalationRequest,
    current_user: dict = Depends(get_current_user),
    db: SessionLocal = Depends(get_db)
):
    try:
        escalation_id = await rag_pipeline.create_escalation(
            session_id=escalation.session_id,
            user_id=current_user["id"],
            issue_summary=escalation.issue_summary,
            priority=escalation.priority,
            category=escalation.category,
            db=db
        )
        
        return {
            "status": "success",
            "escalation_id": escalation_id,
            "message": "Issue escalated successfully. A support agent will contact you soon."
        }
        
    except Exception as e:
        logger.error(f"Error escalating issue: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error escalating issue: {str(e)}"
        )

@app.get("/api/v1/sessions/{session_id}/history")
async def get_chat_history(
    session_id: str,
    current_user: dict = Depends(get_current_user),
    db: SessionLocal = Depends(get_db)
):
    try:
        history = await rag_pipeline.get_chat_history(
            session_id=session_id,
            user_id=current_user["id"],
            db=db
        )
        
        return {"session_id": session_id, "messages": history}
        
    except Exception as e:
        logger.error(f"Error retrieving chat history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving chat history: {str(e)}"
        )

@app.get("/api/v1/analytics/dashboard")
async def get_analytics_dashboard(
    current_user: dict = Depends(get_current_user),
    db: SessionLocal = Depends(get_db)
):
    if current_user.get("role") not in ["admin", "agent"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    
    try:
        analytics = await rag_pipeline.get_analytics_data(db=db)
        return analytics
        
    except Exception as e:
        logger.error(f"Error retrieving analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving analytics: {str(e)}"
        )

# ============================================================================
# FRESHWORKS/FRESHSERVICE INTEGRATION ENDPOINTS
# ============================================================================

@app.post("/api/v1/freshworks/create-ticket")
async def create_freshworks_ticket(
    escalation: EscalationRequest,
    current_user: dict = Depends(get_current_user),
    db: SessionLocal = Depends(get_db)
):
    """Create a Freshservice ticket from escalated chat conversation"""
    try:
        # Prepare escalation data for Freshworks
        escalation_data = {
            "session_id": escalation.session_id,
            "user_email": current_user.get("email", "<EMAIL>"),
            "user_name": current_user.get("display_name", "Unknown User"),
            "issue_summary": escalation.issue_summary,
            "category": escalation.category or "General Support",
            "priority": escalation.priority,
            "department": current_user.get("department"),
            "confidence_score": 0.0  # Will be set by RAG pipeline if available
        }
        
        # Create Freshservice ticket
        result = await create_ticket_from_escalation(escalation_data, db)
        
        logger.info(f"Created Freshservice ticket #{result['ticket']['display_id']} for user {current_user['email']}")
        
        return {
            "status": "success",
            "freshservice_ticket_id": result["ticket"]["id"],
            "display_id": result["ticket"]["display_id"],
            "ticket_url": f"https://{FreshserviceConfig().domain}/helpdesk/tickets/{result['ticket']['display_id']}",
            "message": f"Ticket #{result['ticket']['display_id']} created successfully in Freshservice"
        }
        
    except FreshserviceAPIError as e:
        logger.error(f"Freshservice API error: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Unable to create Freshservice ticket: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error creating Freshservice ticket: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating Freshservice ticket: {str(e)}"
        )

@app.post("/api/v1/freshworks/update-ticket-feedback")
async def update_freshworks_ticket_feedback(
    feedback: UserFeedback,
    current_user: dict = Depends(get_current_user),
    db: SessionLocal = Depends(get_db)
):
    """Update Freshservice ticket based on user feedback"""
    try:
        # Extract Freshservice ticket ID from message metadata
        # This would typically be stored when the ticket is created
        from sqlalchemy import text
        
        # Find the Freshservice ticket ID associated with this message
        query = text("""
            SELECT integration_data 
            FROM test_tickets 
            WHERE session_id = (
                SELECT session_id FROM chat_messages 
                WHERE message_id = :message_id
            )
            AND integration_data IS NOT NULL
            AND integration_data::text LIKE '%freshservice_id%'
            LIMIT 1
        """)
        
        result = db.execute(query, {"message_id": feedback.message_id})
        row = result.fetchone()
        
        if not row:
            return {
                "status": "skipped",
                "message": "No associated Freshservice ticket found"
            }
        
        integration_data = json.loads(row[0])
        freshservice_ticket_id = integration_data.get("freshservice_id")
        
        if not freshservice_ticket_id:
            return {
                "status": "skipped", 
                "message": "No Freshservice ticket ID in integration data"
            }
        
        # Prepare feedback data
        feedback_data = {
            "rating": feedback.rating,
            "feedback_type": feedback.feedback_type,
            "comment": feedback.comment
        }
        
        # Update Freshservice ticket
        result = await update_ticket_from_chat_feedback(
            ticket_id=freshservice_ticket_id,
            feedback_data=feedback_data,
            db=db
        )
        
        return {
            "status": "success",
            "freshservice_ticket_id": freshservice_ticket_id,
            "message": "Freshservice ticket updated with user feedback"
        }
        
    except Exception as e:
        logger.error(f"Error updating Freshservice ticket with feedback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating Freshservice ticket: {str(e)}"
        )

@app.get("/api/v1/freshworks/sync-tickets")
async def sync_freshworks_tickets(
    current_user: dict = Depends(get_current_user),
    db: SessionLocal = Depends(get_db),
    hours_back: int = 24
):
    """Sync recent Freshservice tickets with local database"""
    
    if current_user.get("role") not in ["admin", "agent"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    
    try:
        config = FreshserviceConfig()
        updated_since = datetime.utcnow() - timedelta(hours=hours_back)
        
        async with FreshserviceIntegration(config) as fs:
            # Get recent tickets from Freshservice
            tickets_result = await fs.list_tickets(
                updated_since=updated_since,
                per_page=100,
                filters={"include": "description,custom_fields"}
            )
            
            synced_count = 0
            for ticket in tickets_result.get("tickets", []):
                try:
                    await fs.sync_ticket_with_local_db(
                        freshservice_ticket_id=ticket["id"],
                        local_db=db
                    )
                    synced_count += 1
                except Exception as e:
                    logger.error(f"Error syncing ticket {ticket['id']}: {e}")
                    continue
            
            return {
                "status": "success",
                "synced_tickets": synced_count,
                "total_tickets": len(tickets_result.get("tickets", [])),
                "hours_back": hours_back,
                "message": f"Synchronized {synced_count} tickets from Freshservice"
            }
    
    except FreshserviceAPIError as e:
        logger.error(f"Freshservice API error during sync: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Freshservice sync failed: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error syncing Freshservice tickets: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error syncing tickets: {str(e)}"
        )

@app.get("/api/v1/freshworks/ticket/{ticket_id}")
async def get_freshworks_ticket(
    ticket_id: int,
    current_user: dict = Depends(get_current_user),
    include_conversations: bool = False
):
    """Get Freshservice ticket details"""
    
    try:
        config = FreshserviceConfig()
        include_params = ["requester"] if not include_conversations else ["requester", "conversations"]
        
        async with FreshserviceIntegration(config) as fs:
            ticket = await fs.get_ticket(ticket_id, include=include_params)
            
            return {
                "status": "success",
                "ticket": ticket["ticket"],
                "retrieved_at": datetime.utcnow().isoformat()
            }
    
    except FreshserviceAPIError as e:
        logger.error(f"Freshservice API error getting ticket {ticket_id}: {e}")
        if e.status_code == 404:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Freshservice ticket {ticket_id} not found"
            )
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Freshservice API error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error retrieving Freshservice ticket {ticket_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving ticket: {str(e)}"
        )

@app.get("/api/v1/freshworks/config/test")
async def test_freshworks_connection(
    current_user: dict = Depends(get_current_user)
):
    """Test Freshservice API connection and configuration"""
    
    if current_user.get("role") not in ["admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    try:
        config = FreshserviceConfig()
        
        # Test connection by getting a single ticket (just to verify API works)
        async with FreshserviceIntegration(config) as fs:
            # Try to get tickets (limited to 1) to test connection
            test_result = await fs.list_tickets(per_page=1)
            
            return {
                "status": "success",
                "message": "Freshservice connection successful",
                "config": {
                    "domain": config.domain,
                    "api_version": config.api_version,
                    "base_url": config.base_url,
                    "api_key_configured": bool(config.api_key),
                    "rate_limit_per_minute": config.rate_limit_per_minute
                },
                "test_response": {
                    "tickets_accessible": len(test_result.get("tickets", [])),
                    "connection_time": datetime.utcnow().isoformat()
                }
            }
    
    except FreshserviceAPIError as e:
        return {
            "status": "error",
            "message": f"Freshservice connection failed: {str(e)}",
            "error_details": {
                "status_code": e.status_code,
                "error_detail": e.error_detail
            },
            "config": {
                "domain": config.domain,
                "api_key_configured": bool(config.api_key)
            }
        }
    except Exception as e:
        logger.error(f"Error testing Freshservice connection: {str(e)}")
        return {
            "status": "error",
            "message": f"Connection test failed: {str(e)}",
            "config": {
                "domain": config.domain,
                "api_key_configured": bool(config.api_key)
            }
        }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
