#!/usr/bin/env python3
"""
Database Setup Script for Enterprise Technical Support Platform
Sets up PostgreSQL database with the enterprise schema
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseSetup:
    """Handles database setup for the enterprise platform"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.schema_file = self.base_dir / 'schema.sql'
        
        # Default database configuration
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': '',
            'database': 'enterprise_support'
        }
        
        # Load from environment if available
        self.load_config_from_env()
    
    def load_config_from_env(self):
        """Load database configuration from environment variables"""
        env_file = self.base_dir / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('DATABASE_URL='):
                        # Parse DATABASE_URL
                        db_url = line.split('=', 1)[1]
                        if db_url.startswith('postgresql://'):
                            # Extract components from URL
                            # postgresql://user:password@host:port/database
                            try:
                                import urllib.parse
                                parsed = urllib.parse.urlparse(db_url)
                                if parsed.username:
                                    self.db_config['user'] = parsed.username
                                if parsed.password:
                                    self.db_config['password'] = parsed.password
                                if parsed.hostname:
                                    self.db_config['host'] = parsed.hostname
                                if parsed.port:
                                    self.db_config['port'] = parsed.port
                                if parsed.path and len(parsed.path) > 1:
                                    self.db_config['database'] = parsed.path[1:]  # Remove leading /
                            except Exception as e:
                                logger.warning(f"Failed to parse DATABASE_URL: {e}")
        
        # Override with individual environment variables
        env_mappings = {
            'DB_HOST': 'host',
            'DB_PORT': 'port',
            'DB_USER': 'user',
            'DB_PASSWORD': 'password',
            'DB_NAME': 'database'
        }
        
        for env_var, config_key in env_mappings.items():
            if env_var in os.environ:
                self.db_config[config_key] = os.environ[env_var]
    
    def prompt_for_credentials(self):
        """Prompt user for database credentials"""
        print("\n🔐 Database Configuration")
        print("=" * 40)
        
        # Get database credentials from user
        host = input(f"Database host [{self.db_config['host']}]: ").strip()
        if host:
            self.db_config['host'] = host
        
        port = input(f"Database port [{self.db_config['port']}]: ").strip()
        if port:
            self.db_config['port'] = int(port)
        
        user = input(f"Database user [{self.db_config['user']}]: ").strip()
        if user:
            self.db_config['user'] = user
        
        password = input("Database password: ").strip()
        if password:
            self.db_config['password'] = password
        
        database = input(f"Database name [{self.db_config['database']}]: ").strip()
        if database:
            self.db_config['database'] = database
    
    def test_connection(self, database='postgres') -> bool:
        """Test database connection"""
        try:
            conn = psycopg2.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=database
            )
            conn.close()
            return True
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def create_database(self) -> bool:
        """Create the enterprise_support database"""
        logger.info(f"🗄️  Creating database '{self.db_config['database']}'...")
        
        try:
            # Connect to postgres database to create new database
            conn = psycopg2.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database='postgres'
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            
            cursor = conn.cursor()
            
            # Check if database exists
            cursor.execute(
                "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
                (self.db_config['database'],)
            )
            
            if cursor.fetchone():
                logger.info(f"✅ Database '{self.db_config['database']}' already exists")
            else:
                # Create database
                cursor.execute(f'CREATE DATABASE "{self.db_config["database"]}"')
                logger.info(f"✅ Database '{self.db_config['database']}' created")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create database: {e}")
            return False
    
    def enable_extensions(self) -> bool:
        """Enable required PostgreSQL extensions"""
        logger.info("🔧 Enabling PostgreSQL extensions...")
        
        try:
            conn = psycopg2.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            cursor = conn.cursor()
            
            # Enable required extensions
            extensions = [
                'uuid-ossp',
                'pg_stat_statements'
            ]
            
            for ext in extensions:
                try:
                    cursor.execute(f'CREATE EXTENSION IF NOT EXISTS "{ext}"')
                    logger.info(f"✅ Extension '{ext}' enabled")
                except Exception as e:
                    logger.warning(f"⚠️  Could not enable extension '{ext}': {e}")
            
            conn.commit()
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to enable extensions: {e}")
            return False
    
    def run_schema(self) -> bool:
        """Run the schema.sql file"""
        logger.info("📋 Running database schema...")
        
        if not self.schema_file.exists():
            logger.error(f"❌ Schema file not found: {self.schema_file}")
            return False
        
        try:
            # Use psql command if available
            cmd = [
                'psql',
                '-h', self.db_config['host'],
                '-p', str(self.db_config['port']),
                '-U', self.db_config['user'],
                '-d', self.db_config['database'],
                '-f', str(self.schema_file)
            ]
            
            env = os.environ.copy()
            if self.db_config['password']:
                env['PGPASSWORD'] = self.db_config['password']
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Schema applied successfully")
                return True
            else:
                logger.error(f"❌ Schema application failed: {result.stderr}")
                return False
                
        except FileNotFoundError:
            # Fallback to Python execution
            logger.info("📝 psql not found, using Python to execute schema...")
            return self.run_schema_python()
    
    def run_schema_python(self) -> bool:
        """Run schema using Python psycopg2"""
        try:
            conn = psycopg2.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            cursor = conn.cursor()
            
            # Read and execute schema file
            with open(self.schema_file, 'r') as f:
                schema_sql = f.read()
            
            # Split by semicolon and execute each statement
            statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
            
            for i, statement in enumerate(statements):
                try:
                    cursor.execute(statement)
                    if i % 10 == 0:  # Progress indicator
                        logger.info(f"📊 Executed {i+1}/{len(statements)} statements")
                except Exception as e:
                    logger.warning(f"⚠️  Statement {i+1} failed: {e}")
                    # Continue with next statement
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info("✅ Schema applied successfully using Python")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to apply schema: {e}")
            return False
    
    def verify_installation(self) -> bool:
        """Verify the database installation"""
        logger.info("🔍 Verifying database installation...")
        
        try:
            conn = psycopg2.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            
            cursor = conn.cursor()
            
            # Check key tables
            key_tables = [
                'organizations',
                'users',
                'tickets',
                'ticket_activities',
                'automation_rules'
            ]
            
            for table in key_tables:
                cursor.execute(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = %s",
                    (table,)
                )
                if cursor.fetchone()[0] == 0:
                    logger.error(f"❌ Table '{table}' not found")
                    return False
                else:
                    logger.info(f"✅ Table '{table}' exists")
            
            # Check sample data
            cursor.execute("SELECT COUNT(*) FROM organizations")
            org_count = cursor.fetchone()[0]
            logger.info(f"📊 Organizations: {org_count}")
            
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            logger.info(f"👥 Users: {user_count}")
            
            cursor.close()
            conn.close()
            
            logger.info("✅ Database verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Database verification failed: {e}")
            return False
    
    def update_env_file(self):
        """Update .env file with database configuration"""
        logger.info("📝 Updating .env file...")
        
        env_file = self.base_dir / '.env'
        database_url = (
            f"postgresql://{self.db_config['user']}:{self.db_config['password']}"
            f"@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
        )
        
        if env_file.exists():
            # Update existing file
            with open(env_file, 'r') as f:
                lines = f.readlines()
            
            # Update DATABASE_URL line
            updated = False
            for i, line in enumerate(lines):
                if line.startswith('DATABASE_URL='):
                    lines[i] = f"DATABASE_URL={database_url}\n"
                    updated = True
                    break
            
            if not updated:
                lines.append(f"DATABASE_URL={database_url}\n")
            
            with open(env_file, 'w') as f:
                f.writelines(lines)
        else:
            # Create new file
            with open(env_file, 'w') as f:
                f.write(f"DATABASE_URL={database_url}\n")
        
        logger.info("✅ .env file updated")
    
    def run_setup(self) -> bool:
        """Run the complete database setup"""
        logger.info("🗄️  Starting Database Setup for Enterprise Platform")
        
        # Test initial connection
        if not self.test_connection():
            logger.info("🔐 Please provide database credentials:")
            self.prompt_for_credentials()
            
            if not self.test_connection():
                logger.error("❌ Could not connect to PostgreSQL server")
                return False
        
        logger.info("✅ Connected to PostgreSQL server")
        
        # Setup steps
        steps = [
            ("Create Database", self.create_database),
            ("Enable Extensions", self.enable_extensions),
            ("Apply Schema", self.run_schema),
            ("Verify Installation", self.verify_installation)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"📍 Step: {step_name}")
            if not step_func():
                logger.error(f"❌ Database setup failed at step: {step_name}")
                return False
        
        # Update .env file
        self.update_env_file()
        
        print("\n" + "="*50)
        print("🎉 DATABASE SETUP COMPLETE!")
        print("="*50)
        print(f"📊 Database: {self.db_config['database']}")
        print(f"🌐 Host: {self.db_config['host']}:{self.db_config['port']}")
        print(f"👤 User: {self.db_config['user']}")
        print("\n✅ Ready to start the enterprise platform!")
        print("="*50)
        
        return True

def main():
    """Main function"""
    setup = DatabaseSetup()
    
    if setup.run_setup():
        logger.info("✅ Database setup completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Database setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
