"""
Azure AD Authentication Module for SharePoint Integration
Handles authentication, authorization, and token validation
"""

import jwt
import httpx
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import HTTPException, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import json
import os
from functools import lru_cache
import asyncio

logger = logging.getLogger(__name__)

class UserInfo(BaseModel):
    """User information from Azure AD"""
    id: str
    email: str
    name: str
    roles: List[str]
    departments: List[str]
    tenant_id: str
    app_roles: List[str]
    permissions: List[str]

class AzureADConfig:
    """Azure AD configuration"""
    
    def __init__(self):
        self.tenant_id = os.getenv("AZURE_TENANT_ID", "")
        self.client_id = os.getenv("AZURE_CLIENT_ID", "")
        self.client_secret = os.getenv("AZURE_CLIENT_SECRET", "")
        
        # SharePoint specific
        self.sharepoint_tenant_id = os.getenv("SHAREPOINT_TENANT_ID", self.tenant_id)
        self.sharepoint_client_id = os.getenv("SHAREPOINT_CLIENT_ID", self.client_id)
        
        # Azure AD endpoints
        self.authority = f"https://login.microsoftonline.com/{self.tenant_id}"
        self.discovery_endpoint = f"{self.authority}/v2.0/.well-known/openid_configuration"
        self.graph_endpoint = "https://graph.microsoft.com/v1.0"
        self.sharepoint_endpoint = f"https://graph.microsoft.com/v1.0/sites/root"
        
        # Validation settings
        self.issuer = f"https://login.microsoftonline.com/{self.tenant_id}/v2.0"
        self.audience = self.client_id
        
        # Token settings
        self.algorithm = "RS256"
        self.leeway = 30  # seconds

class AzureADAuthenticator:
    """Azure AD authentication handler"""
    
    def __init__(self, config: Optional[AzureADConfig] = None):
        self.config = config or AzureADConfig()
        self._jwks_cache = {}
        self._jwks_cache_time = None
        self._user_cache = {}
        
    async def get_jwks(self) -> Dict[str, Any]:
        """Get JSON Web Key Set from Azure AD"""
        
        # Check cache (refresh every hour)
        now = datetime.utcnow()
        if (self._jwks_cache_time and 
            (now - self._jwks_cache_time).total_seconds() < 3600 and 
            self._jwks_cache):
            return self._jwks_cache
        
        try:
            # Get OpenID configuration
            async with httpx.AsyncClient() as client:
                config_response = await client.get(self.config.discovery_endpoint)
                config_response.raise_for_status()
                openid_config = config_response.json()
                
                # Get JWKS
                jwks_uri = openid_config["jwks_uri"]
                jwks_response = await client.get(jwks_uri)
                jwks_response.raise_for_status()
                
                self._jwks_cache = jwks_response.json()
                self._jwks_cache_time = now
                
                return self._jwks_cache
                
        except Exception as e:
            logger.error(f"Failed to get JWKS: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to validate token - authentication service unavailable"
            )
    
    def get_signing_key(self, token_header: Dict[str, Any]) -> str:
        """Get the signing key for token validation"""
        
        kid = token_header.get("kid")
        if not kid:
            raise HTTPException(
                status_code=401,
                detail="Token header missing 'kid' claim"
            )
        
        # Find the key in JWKS
        for key in self._jwks_cache.get("keys", []):
            if key.get("kid") == kid:
                # Convert JWK to PEM format
                return jwt.algorithms.RSAAlgorithm.from_jwk(json.dumps(key))
        
        raise HTTPException(
            status_code=401,
            detail="Unable to find signing key"
        )
    
    async def validate_token(self, token: str) -> Dict[str, Any]:
        """Validate JWT token from Azure AD"""
        
        try:
            # Decode header to get key ID
            unverified_header = jwt.get_unverified_header(token)
            
            # Get JWKS
            await self.get_jwks()
            
            # Get signing key
            signing_key = self.get_signing_key(unverified_header)
            
            # Validate and decode token
            payload = jwt.decode(
                token,
                signing_key,
                algorithms=[self.config.algorithm],
                audience=self.config.audience,
                issuer=self.config.issuer,
                leeway=self.config.leeway,
                options={
                    "verify_signature": True,
                    "verify_exp": True,
                    "verify_aud": True,
                    "verify_iss": True
                }
            )
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=401,
                detail="Token has expired"
            )
        except jwt.InvalidAudienceError:
            raise HTTPException(
                status_code=401,
                detail="Invalid token audience"
            )
        except jwt.InvalidIssuerError:
            raise HTTPException(
                status_code=401,
                detail="Invalid token issuer"
            )
        except jwt.InvalidTokenError as e:
            raise HTTPException(
                status_code=401,
                detail=f"Invalid token: {str(e)}"
            )
    
    async def get_user_info(self, token: str, token_payload: Dict[str, Any]) -> UserInfo:
        """Get detailed user information from Microsoft Graph"""
        
        user_id = token_payload.get("oid") or token_payload.get("sub")
        
        # Check cache
        if user_id in self._user_cache:
            cache_entry = self._user_cache[user_id]
            if (datetime.utcnow() - cache_entry["timestamp"]).total_seconds() < 1800:  # 30 minutes
                return cache_entry["user_info"]
        
        try:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                # Get user profile
                user_response = await client.get(
                    f"{self.config.graph_endpoint}/me",
                    headers=headers
                )
                
                if user_response.status_code == 200:
                    user_data = user_response.json()
                else:
                    # Fallback to token claims if Graph API call fails
                    user_data = {
                        "id": user_id,
                        "userPrincipalName": token_payload.get("upn", ""),
                        "displayName": token_payload.get("name", ""),
                        "department": token_payload.get("department", "")
                    }
                
                # Get user's group memberships for roles
                groups = []
                try:
                    groups_response = await client.get(
                        f"{self.config.graph_endpoint}/me/memberOf",
                        headers=headers
                    )
                    if groups_response.status_code == 200:
                        groups_data = groups_response.json()
                        groups = [group["displayName"] for group in groups_data.get("value", [])]
                except Exception as e:
                    logger.warning(f"Failed to get user groups: {e}")
                
                # Map groups to roles
                roles = self._map_groups_to_roles(groups)
                
                # Get app-specific roles from token
                app_roles = token_payload.get("roles", [])
                
                # Determine permissions
                permissions = self._determine_permissions(roles, app_roles)
                
                user_info = UserInfo(
                    id=user_data.get("id", user_id),
                    email=user_data.get("userPrincipalName", token_payload.get("upn", "")),
                    name=user_data.get("displayName", token_payload.get("name", "")),
                    roles=roles,
                    departments=[user_data.get("department", token_payload.get("department", ""))],
                    tenant_id=token_payload.get("tid", self.config.tenant_id),
                    app_roles=app_roles,
                    permissions=permissions
                )
                
                # Cache user info
                self._user_cache[user_id] = {
                    "user_info": user_info,
                    "timestamp": datetime.utcnow()
                }
                
                return user_info
                
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            
            # Return minimal user info from token
            return UserInfo(
                id=user_id,
                email=token_payload.get("upn", ""),
                name=token_payload.get("name", ""),
                roles=["user"],
                departments=[token_payload.get("department", "")],
                tenant_id=token_payload.get("tid", self.config.tenant_id),
                app_roles=token_payload.get("roles", []),
                permissions=["user.read"]
            )
    
    def _map_groups_to_roles(self, groups: List[str]) -> List[str]:
        """Map Azure AD groups to application roles"""
        
        role_mapping = {
            # IT Staff groups
            "IT Support": ["staff", "user"],
            "IT Administrators": ["admin", "staff", "user"],
            "Help Desk": ["staff", "user"],
            "System Administrators": ["admin", "staff", "user"],
            
            # Department groups
            "Finance": ["user"],
            "HR": ["user"],
            "Marketing": ["user"],
            "Sales": ["user"],
            
            # Special roles
            "SharePoint Administrators": ["sharepoint_admin", "admin", "staff", "user"],
            "AI Assistant Administrators": ["ai_admin", "admin", "staff", "user"],
        }
        
        roles = set(["user"])  # Default role
        
        for group in groups:
            if group in role_mapping:
                roles.update(role_mapping[group])
        
        return list(roles)
    
    def _determine_permissions(self, roles: List[str], app_roles: List[str]) -> List[str]:
        """Determine user permissions based on roles"""
        
        permissions = set()
        
        # Base permissions for all users
        permissions.add("user.read")
        permissions.add("user_assistant.use")
        
        # Staff permissions
        if "staff" in roles or "ITSupport" in app_roles:
            permissions.update([
                "staff_assistant.use",
                "tickets.read",
                "tickets.update",
                "analytics.view",
                "knowledge.contribute"
            ])
        
        # Admin permissions
        if "admin" in roles or "Administrator" in app_roles:
            permissions.update([
                "admin.all",
                "analytics.admin",
                "config.manage",
                "users.manage",
                "knowledge.admin"
            ])
        
        # SharePoint admin permissions
        if "sharepoint_admin" in roles:
            permissions.update([
                "sharepoint.admin",
                "sites.manage",
                "webparts.deploy"
            ])
        
        # AI admin permissions
        if "ai_admin" in roles:
            permissions.update([
                "ai.admin",
                "models.manage",
                "training.manage"
            ])
        
        return list(permissions)

class SharePointAuthenticator:
    """SharePoint-specific authentication"""
    
    def __init__(self, azure_auth: AzureADAuthenticator):
        self.azure_auth = azure_auth
        
    async def validate_sharepoint_context(self, token: str, site_url: str) -> Dict[str, Any]:
        """Validate that user has access to specific SharePoint site"""
        
        try:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                # Get site information
                site_response = await client.get(
                    f"https://graph.microsoft.com/v1.0/sites/{site_url}",
                    headers=headers
                )
                
                if site_response.status_code == 200:
                    site_data = site_response.json()
                    return {
                        "site_id": site_data.get("id"),
                        "site_name": site_data.get("displayName"),
                        "web_url": site_data.get("webUrl"),
                        "has_access": True
                    }
                else:
                    return {"has_access": False}
                    
        except Exception as e:
            logger.error(f"Failed to validate SharePoint context: {e}")
            return {"has_access": False}

# FastAPI Security Scheme
security = HTTPBearer()

# Global authenticator instance
authenticator = AzureADAuthenticator()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> UserInfo:
    """FastAPI dependency to get current authenticated user"""
    
    token = credentials.credentials
    
    # Validate token
    token_payload = await authenticator.validate_token(token)
    
    # Get user info
    user_info = await authenticator.get_user_info(token, token_payload)
    
    return user_info

def require_permission(permission: str):
    """Decorator to require specific permission"""
    
    def permission_dependency(user: UserInfo = Depends(get_current_user)):
        if permission not in user.permissions:
            raise HTTPException(
                status_code=403,
                detail=f"Permission '{permission}' required"
            )
        return user
    
    return permission_dependency

def require_role(role: str):
    """Decorator to require specific role"""
    
    def role_dependency(user: UserInfo = Depends(get_current_user)):
        if role not in user.roles:
            raise HTTPException(
                status_code=403,
                detail=f"Role '{role}' required"
            )
        return user
    
    return role_dependency

# Permission constants
class Permissions:
    USER_READ = "user.read"
    USER_ASSISTANT_USE = "user_assistant.use"
    STAFF_ASSISTANT_USE = "staff_assistant.use"
    TICKETS_READ = "tickets.read"
    TICKETS_UPDATE = "tickets.update"
    ANALYTICS_VIEW = "analytics.view"
    ANALYTICS_ADMIN = "analytics.admin"
    KNOWLEDGE_CONTRIBUTE = "knowledge.contribute"
    KNOWLEDGE_ADMIN = "knowledge.admin"
    ADMIN_ALL = "admin.all"
    CONFIG_MANAGE = "config.manage"
    USERS_MANAGE = "users.manage"

# Role constants
class Roles:
    USER = "user"
    STAFF = "staff"
    ADMIN = "admin"
    SHAREPOINT_ADMIN = "sharepoint_admin"
    AI_ADMIN = "ai_admin"

# Authentication utilities
async def validate_api_key(request: Request) -> bool:
    """Validate API key for service-to-service calls"""
    
    api_key = request.headers.get("X-API-Key")
    expected_key = os.getenv("API_KEY")
    
    if not api_key or not expected_key:
        return False
    
    return api_key == expected_key

def create_service_token(user_id: str, roles: List[str], duration_hours: int = 24) -> str:
    """Create a service token for internal API calls"""
    
    payload = {
        "sub": user_id,
        "roles": roles,
        "iss": "sharepoint-ai-service",
        "aud": "sharepoint-ai-api",
        "exp": datetime.utcnow() + timedelta(hours=duration_hours),
        "iat": datetime.utcnow(),
        "service": True
    }
    
    secret_key = os.getenv("JWT_SECRET_KEY", "default-secret-key")
    return jwt.encode(payload, secret_key, algorithm="HS256")

async def validate_service_token(token: str) -> Dict[str, Any]:
    """Validate internal service token"""
    
    try:
        secret_key = os.getenv("JWT_SECRET_KEY", "default-secret-key")
        payload = jwt.decode(token, secret_key, algorithms=["HS256"])
        
        if not payload.get("service"):
            raise HTTPException(
                status_code=401,
                detail="Invalid service token"
            )
        
        return payload
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=401,
            detail="Service token has expired"
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=401,
            detail="Invalid service token"
        )

# Development/testing authentication bypass
class MockUser(UserInfo):
    """Mock user for development and testing"""
    
    def __init__(self, user_type: str = "user"):
        if user_type == "admin":
            super().__init__(
                id="mock_admin_123",
                email="<EMAIL>",
                name="Mock Admin",
                roles=["admin", "staff", "user"],
                departments=["IT"],
                tenant_id="mock-tenant",
                app_roles=["Administrator"],
                permissions=[
                    "admin.all", "analytics.admin", "config.manage",
                    "users.manage", "knowledge.admin", "staff_assistant.use",
                    "tickets.read", "tickets.update", "analytics.view",
                    "knowledge.contribute", "user_assistant.use", "user.read"
                ]
            )
        elif user_type == "staff":
            super().__init__(
                id="mock_staff_123",
                email="<EMAIL>",
                name="Mock Staff",
                roles=["staff", "user"],
                departments=["IT"],
                tenant_id="mock-tenant",
                app_roles=["ITSupport"],
                permissions=[
                    "staff_assistant.use", "tickets.read", "tickets.update",
                    "analytics.view", "knowledge.contribute",
                    "user_assistant.use", "user.read"
                ]
            )
        else:
            super().__init__(
                id="mock_user_123",
                email="<EMAIL>",
                name="Mock User",
                roles=["user"],
                departments=["Marketing"],
                tenant_id="mock-tenant",
                app_roles=[],
                permissions=["user_assistant.use", "user.read"]
            )

async def get_mock_user(user_type: str = "user") -> UserInfo:
    """Get mock user for development/testing"""
    return MockUser(user_type)

# Development mode bypass
async def get_current_user_dev(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> UserInfo:
    """Development version of get_current_user with bypass option"""
    
    if os.getenv("DEV_MODE", "false").lower() == "true":
        # Check for mock user header
        mock_user_type = credentials.credentials.lower()
        if mock_user_type in ["admin", "staff", "user"]:
            return await get_mock_user(mock_user_type)
    
    # Use normal authentication
    return await get_current_user(credentials)

# Health check for authentication service
async def auth_health_check() -> Dict[str, Any]:
    """Check health of authentication service"""
    
    try:
        # Test Azure AD discovery endpoint
        async with httpx.AsyncClient() as client:
            response = await client.get(
                authenticator.config.discovery_endpoint,
                timeout=10.0
            )
            
            if response.status_code == 200:
                return {
                    "status": "healthy",
                    "azure_ad": "accessible",
                    "discovery_endpoint": "ok",
                    "tenant_id": authenticator.config.tenant_id
                }
            else:
                return {
                    "status": "degraded",
                    "azure_ad": "inaccessible",
                    "error": f"HTTP {response.status_code}"
                }
                
    except Exception as e:
        return {
            "status": "unhealthy",
            "azure_ad": "error",
            "error": str(e)
        }