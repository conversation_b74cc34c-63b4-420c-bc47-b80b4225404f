#!/usr/bin/env python3
"""
Simplified Enterprise Platform Startup Script
Works with minimal dependencies and no network access required
"""

import os
import sys
import time
import subprocess
import threading
import signal
import logging
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_startup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SimpleServiceManager:
    """Simplified service manager that works with basic dependencies"""
    
    def __init__(self):
        self.services = {}
        self.base_dir = Path(__file__).parent
        self.venv_python = self._get_python_executable()
        self.running = True
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _get_python_executable(self) -> str:
        """Get the correct Python executable"""
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            return sys.executable
        else:
            venv_python = self.base_dir / 'venv' / 'bin' / 'python'
            if venv_python.exists():
                return str(venv_python)
            return sys.executable
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down services...")
        self.running = False
        self.stop_all_services()
        sys.exit(0)
    
    def check_basic_requirements(self) -> bool:
        """Check basic requirements without network dependencies"""
        logger.info("🔍 Checking basic requirements...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            logger.error("❌ Python 3.8+ required")
            return False
        logger.info("✅ Python version OK")
        
        # Check required files
        required_files = [
            'backend/enterprise_api.py',
            'enterprise_dashboard.py',
            'schema.sql'
        ]
        
        for file_path in required_files:
            if not (self.base_dir / file_path).exists():
                logger.error(f"❌ Required file missing: {file_path}")
                return False
        logger.info("✅ All required files present")
        
        return True
    
    def setup_environment(self):
        """Set up environment variables"""
        logger.info("🔧 Setting up environment...")
        
        # Default environment variables
        env_vars = {
            'DATABASE_URL': 'sqlite:///enterprise_support.db',  # Use SQLite as fallback
            'ORGANIZATION_ID': '00000000-0000-0000-0000-000000000001',
            'JWT_SECRET_KEY': 'development-secret-key-change-in-production',
            'API_HOST': '127.0.0.1',
            'API_PORT': '8000',
            'DASHBOARD_PORT': '8501',
            'LOG_LEVEL': 'INFO',
            'PYTHONPATH': str(self.base_dir)
        }
        
        # Load from .env file if it exists
        env_file = self.base_dir / '.env'
        if env_file.exists():
            logger.info("📄 Loading environment from .env file")
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()
        
        # Set environment variables
        for key, value in env_vars.items():
            if key not in os.environ:
                os.environ[key] = value
        
        logger.info("✅ Environment configured")
    
    def check_port_availability(self, port: int) -> bool:
        """Simple port check without psutil"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return True
        except OSError:
            return False
    
    def wait_for_service_simple(self, port: int, timeout: int = 60) -> bool:
        """Simple service health check"""
        import socket
        logger.info(f"⏳ Waiting for service on port {port}...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('127.0.0.1', port))
                    if result == 0:
                        logger.info(f"✅ Service on port {port} is ready")
                        return True
            except Exception:
                pass
            time.sleep(2)
        
        logger.error(f"❌ Service on port {port} failed to start within {timeout} seconds")
        return False
    
    def start_api_service(self) -> bool:
        """Start the API service"""
        logger.info("🚀 Starting API service...")
        
        # Check port availability
        if not self.check_port_availability(8000):
            logger.error("❌ Port 8000 already in use")
            return False
        
        try:
            # Try to start with uvicorn
            cmd = [
                self.venv_python, '-m', 'uvicorn',
                'backend.enterprise_api:app',
                '--host', '127.0.0.1',
                '--port', '8000'
            ]
            
            process = subprocess.Popen(
                cmd,
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.services['api'] = {
                'process': process,
                'port': 8000,
                'name': 'API Server'
            }
            
            # Wait for service to be ready
            if self.wait_for_service_simple(8000, 30):
                logger.info("✅ API service started successfully")
                return True
            else:
                self.stop_service('api')
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to start API service: {e}")
            return False
    
    def start_dashboard_service(self) -> bool:
        """Start the dashboard service"""
        logger.info("🚀 Starting dashboard service...")
        
        # Check port availability
        if not self.check_port_availability(8501):
            logger.error("❌ Port 8501 already in use")
            return False
        
        try:
            # Try to start with streamlit
            cmd = [
                self.venv_python, '-m', 'streamlit', 'run',
                'enterprise_dashboard.py',
                '--server.port', '8501',
                '--server.address', '127.0.0.1',
                '--server.headless', 'true'
            ]
            
            process = subprocess.Popen(
                cmd,
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.services['dashboard'] = {
                'process': process,
                'port': 8501,
                'name': 'Dashboard'
            }
            
            # Wait for service to be ready
            if self.wait_for_service_simple(8501, 30):
                logger.info("✅ Dashboard service started successfully")
                return True
            else:
                self.stop_service('dashboard')
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to start dashboard service: {e}")
            return False
    
    def stop_service(self, service_name: str):
        """Stop a single service"""
        if service_name in self.services:
            service = self.services[service_name]
            process = service['process']
            
            logger.info(f"🛑 Stopping {service['name']}...")
            
            try:
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            
            del self.services[service_name]
            logger.info(f"✅ {service['name']} stopped")
    
    def start_all_services(self) -> bool:
        """Start all services"""
        logger.info("🚀 Starting Enterprise Technical Support Platform (Simple Mode)...")
        
        # Start API service
        if not self.start_api_service():
            logger.error("❌ Failed to start API service")
            return False
        
        # Start dashboard service
        if not self.start_dashboard_service():
            logger.error("❌ Failed to start dashboard service")
            return False
        
        logger.info("🎉 All services started successfully!")
        self.print_service_status()
        return True
    
    def stop_all_services(self):
        """Stop all services"""
        logger.info("🛑 Stopping all services...")
        
        service_names = list(self.services.keys())
        for service_name in service_names:
            self.stop_service(service_name)
        
        logger.info("✅ All services stopped")
    
    def print_service_status(self):
        """Print current service status"""
        print("\n" + "="*60)
        print("🎯 ENTERPRISE TECHNICAL SUPPORT PLATFORM (SIMPLE MODE)")
        print("="*60)
        print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n📊 Service Status:")
        
        for service_name, service in self.services.items():
            status = "🟢 RUNNING"
            print(f"  {service['name']:15} | {status} | Port {service['port']}")
        
        print("\n🌐 Access URLs:")
        if 'api' in self.services:
            print("  📡 API Server:     http://127.0.0.1:8000")
            print("  📚 API Docs:      http://127.0.0.1:8000/docs")
        
        if 'dashboard' in self.services:
            print("  📊 Dashboard:     http://127.0.0.1:8501")
        
        print("\n💡 Quick Commands:")
        print("  • Press Ctrl+C to stop all services")
        print("  • Check logs: tail -f simple_startup.log")
        print("\n⚠️  Note: Running in simple mode with minimal dependencies")
        print("="*60)
    
    def monitor_services(self):
        """Monitor services"""
        while self.running:
            for service_name, service in list(self.services.items()):
                process = service['process']
                if process.poll() is not None:
                    logger.warning(f"⚠️  {service['name']} has stopped unexpectedly")
                    if self.running:
                        logger.info(f"🔄 Restarting {service['name']}...")
                        self.stop_service(service_name)
                        time.sleep(2)
                        if service_name == 'api':
                            self.start_api_service()
                        elif service_name == 'dashboard':
                            self.start_dashboard_service()
            
            time.sleep(10)

def main():
    """Main function"""
    print("🎯 Enterprise Technical Support Platform (Simple Startup)")
    print("=" * 55)
    
    manager = SimpleServiceManager()
    
    # Check basic requirements
    if not manager.check_basic_requirements():
        logger.error("❌ Basic requirements not met.")
        print("\n💡 Try installing missing dependencies:")
        print("   pip install fastapi uvicorn streamlit")
        sys.exit(1)
    
    # Setup environment
    manager.setup_environment()
    
    # Start all services
    if not manager.start_all_services():
        logger.error("❌ Failed to start services")
        sys.exit(1)
    
    # Monitor services
    try:
        manager.monitor_services()
    except KeyboardInterrupt:
        logger.info("👋 Shutdown requested by user")
    finally:
        manager.stop_all_services()

if __name__ == "__main__":
    main()
