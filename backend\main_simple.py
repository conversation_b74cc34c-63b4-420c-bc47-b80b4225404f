#!/usr/bin/env python3
"""
Simplified FastAPI backend for Technical Support Chatbot
Works without heavy ML dependencies for initial testing
"""

from fastapi import FastAP<PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uvicorn
import os
import json
from datetime import datetime, timedelta
import logging
import uuid
import random
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple data models
class ChatRequest(BaseModel):
    message: str
    session_id: str
    user_context: Optional[Dict[str, Any]] = None

class ChatResponse(BaseModel):
    message: str
    session_id: str
    sources: List[str] = []
    confidence_score: float = 0.8
    response_time_ms: int = 100
    suggestions: List[str] = []
    needs_escalation: bool = False

class UserFeedback(BaseModel):
    session_id: str
    message_id: str
    rating: int = Field(ge=1, le=5)
    feedback_text: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    uptime_seconds: float

# Create FastAPI app
app = FastAPI(
    title="Technical Support Chatbot API",
    description="AI-powered technical support assistant (Simplified Version)",
    version="1.0.0-simple"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple knowledge base for fallback responses
KNOWLEDGE_BASE = {
    "password": {
        "response": "To reset your password: 1) Go to the login page, 2) Click 'Forgot Password', 3) Enter your email address, 4) Check your email for reset instructions, 5) Follow the link to create a new password. Contact IT support if you need further assistance.",
        "confidence": 0.9,
        "sources": ["IT Security Policy", "Password Reset Guide"],
        "suggestions": ["Check spam folder for reset email", "Contact IT if email not received", "Use strong password requirements"]
    },
    "email": {
        "response": "For email setup: 1) Open your email client, 2) Go to account settings, 3) Add a new account with your credentials, 4) The system will auto-configure most settings. For mobile setup, download the company email app from your device's app store.",
        "confidence": 0.8,
        "sources": ["Email Configuration Guide", "Mobile Device Policy"],
        "suggestions": ["Check internet connection", "Verify email address", "Contact IT for server settings"]
    },
    "software": {
        "response": "For software installation: 1) Contact IT support with the software name and business justification, 2) Wait for approval from your manager, 3) IT will install the software remotely, 4) Follow company software policies. Self-service options may be available through the company portal.",
        "confidence": 0.8,
        "sources": ["Software Installation Policy", "IT Service Catalog"],
        "suggestions": ["Check if software is pre-approved", "Submit request through IT portal", "Provide business justification"]
    },
    "network": {
        "response": "For network connectivity issues: 1) Check all cable connections, 2) Restart your computer, 3) Try a different network port or WiFi network, 4) Run network troubleshooter, 5) Contact IT if the problem persists. Check if other devices have similar issues.",
        "confidence": 0.7,
        "sources": ["Network Troubleshooting Guide", "IT Support Manual"],
        "suggestions": ["Try ethernet instead of WiFi", "Check with colleagues", "Restart network equipment"]
    },
    "vpn": {
        "response": "For VPN connection issues: 1) Check your internet connection, 2) Verify your VPN credentials, 3) Try connecting to a different VPN server, 4) Restart the VPN client application, 5) Contact IT support if problems persist. Ensure you're using the company-approved VPN client.",
        "confidence": 0.8,
        "sources": ["VPN Setup Guide", "Remote Access Policy"],
        "suggestions": ["Try different server location", "Update VPN client", "Check firewall settings"]
    },
    "printer": {
        "response": "For printer setup and issues: 1) Ensure printer is powered on and connected to network, 2) Add printer through system settings, 3) Install latest drivers if needed, 4) Check paper and toner levels, 5) Contact IT for complex printer issues or driver installation.",
        "confidence": 0.7,
        "sources": ["Printer Setup Guide", "Office Equipment Manual"],
        "suggestions": ["Check printer queue", "Restart print spooler", "Try different USB port"]
    }
}

# Application state
app_start_time = datetime.now()
session_store = {}

@app.on_event("startup")
async def startup_event():
    """Initialize the application"""
    logger.info("Starting Technical Support Chatbot API (Simplified Version)")
    logger.info("✅ No heavy ML dependencies required")
    logger.info("✅ Using rule-based responses with fallback logic")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Technical Support Chatbot API")

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint"""
    return {
        "message": "Technical Support Chatbot API (Simplified Version)",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    uptime = (datetime.now() - app_start_time).total_seconds()
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0-simple",
        uptime_seconds=uptime
    )

def generate_smart_response(message: str, session_id: str, user_context: Optional[Dict] = None) -> ChatResponse:
    """Generate intelligent response using keyword matching and rules"""
    message_lower = message.lower()
    
    # Find best matching knowledge base entry
    best_match = None
    best_score = 0
    
    for key, kb_entry in KNOWLEDGE_BASE.items():
        if key in message_lower:
            # Simple scoring based on keyword presence
            score = message_lower.count(key) * 0.3
            if "help" in message_lower or "how" in message_lower:
                score += 0.2
            if "problem" in message_lower or "issue" in message_lower:
                score += 0.1
            
            if score > best_score:
                best_score = score
                best_match = kb_entry
    
    # Generate response
    if best_match and best_score > 0.2:
        response_text = best_match["response"]
        confidence = best_match["confidence"] * min(1.0, best_score + 0.5)
        sources = best_match["sources"]
        suggestions = best_match["suggestions"]
        needs_escalation = confidence < 0.6
    else:
        # Fallback response
        response_text = f"I understand your question about '{message}'. While I don't have a specific answer in my knowledge base, I recommend contacting IT support for personalized assistance. They can provide step-by-step guidance for your specific situation."
        confidence = 0.4
        sources = ["General IT Support"]
        suggestions = [
            "Contact IT support directly",
            "Check the company knowledge base",
            "Submit a detailed support ticket"
        ]
        needs_escalation = True
    
    # Add user context to response if available
    if user_context:
        department = user_context.get("department", "your department")
        response_text += f"\n\nFor {department}-specific guidance, your local IT support team can provide additional assistance."
    
    # Simulate processing time
    response_time = random.randint(800, 1500)
    
    return ChatResponse(
        message=response_text,
        session_id=session_id,
        sources=sources,
        confidence_score=confidence,
        response_time_ms=response_time,
        suggestions=suggestions,
        needs_escalation=needs_escalation
    )

@app.post("/api/v1/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint"""
    try:
        start_time = time.time()
        
        # Store session info
        if request.session_id not in session_store:
            session_store[request.session_id] = {
                "created_at": datetime.now(),
                "message_count": 0,
                "user_context": request.user_context
            }
        
        session_store[request.session_id]["message_count"] += 1
        session_store[request.session_id]["last_message"] = datetime.now()
        
        # Generate response
        response = generate_smart_response(
            request.message, 
            request.session_id, 
            request.user_context
        )
        
        # Log interaction
        processing_time = (time.time() - start_time) * 1000
        logger.info(f"Chat processed in {processing_time:.1f}ms - Session: {request.session_id[:8]} - Confidence: {response.confidence_score:.2f}")
        
        return response
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during chat processing"
        )

@app.post("/api/v1/feedback")
async def submit_feedback(feedback: UserFeedback):
    """Submit user feedback"""
    try:
        # Store feedback (in production, this would go to a database)
        feedback_id = str(uuid.uuid4())
        logger.info(f"Feedback received: {feedback.rating}/5 for session {feedback.session_id[:8]}")
        
        return {
            "feedback_id": feedback_id,
            "message": "Thank you for your feedback!",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error submitting feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error submitting feedback"
        )

@app.get("/api/v1/sessions")
async def get_sessions():
    """Get active chat sessions"""
    try:
        # Return session statistics
        active_sessions = len(session_store)
        total_messages = sum(session["message_count"] for session in session_store.values())
        
        return {
            "active_sessions": active_sessions,
            "total_messages": total_messages,
            "sessions": [
                {
                    "session_id": session_id[:8] + "...",
                    "created_at": session["created_at"].isoformat(),
                    "message_count": session["message_count"],
                    "last_message": session.get("last_message", session["created_at"]).isoformat()
                }
                for session_id, session in list(session_store.items())[-10:]  # Last 10 sessions
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving sessions"
        )

@app.get("/api/v1/knowledge")
async def get_knowledge_topics():
    """Get available knowledge topics"""
    return {
        "topics": list(KNOWLEDGE_BASE.keys()),
        "total_entries": len(KNOWLEDGE_BASE),
        "coverage": [
            {
                "topic": topic,
                "confidence": entry["confidence"],
                "sources": len(entry["sources"])
            }
            for topic, entry in KNOWLEDGE_BASE.items()
        ]
    }

if __name__ == "__main__":
    # For development
    uvicorn.run(
        "backend.main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )