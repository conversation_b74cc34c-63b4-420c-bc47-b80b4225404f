#!/usr/bin/env python3
"""
Offline Installation Script for Enterprise Platform
Installs packages using local wheels or alternative methods when network is unavailable
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_package_installed(package_name):
    """Check if a package is already installed"""
    try:
        __import__(package_name.replace('-', '_'))
        return True
    except ImportError:
        return False

def install_with_fallback(package_name, fallback_command=None):
    """Try to install a package with fallback options"""
    logger.info(f"Checking {package_name}...")
    
    # Check if already installed
    if check_package_installed(package_name):
        logger.info(f"✅ {package_name} already installed")
        return True
    
    # Try pip install
    try:
        logger.info(f"Installing {package_name}...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package_name, '--no-deps'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info(f"✅ {package_name} installed successfully")
            return True
        else:
            logger.warning(f"⚠️  Failed to install {package_name}: {result.stderr}")
    except subprocess.TimeoutExpired:
        logger.warning(f"⚠️  Installation of {package_name} timed out")
    except Exception as e:
        logger.warning(f"⚠️  Error installing {package_name}: {e}")
    
    # Try fallback command if provided
    if fallback_command:
        try:
            logger.info(f"Trying fallback for {package_name}...")
            result = subprocess.run(fallback_command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ {package_name} installed via fallback")
                return True
        except Exception as e:
            logger.warning(f"⚠️  Fallback failed for {package_name}: {e}")
    
    logger.error(f"❌ Could not install {package_name}")
    return False

def main():
    """Main installation function"""
    print("🔧 Offline Installation for Enterprise Platform")
    print("=" * 50)
    
    # Essential packages with fallbacks
    packages = [
        {
            'name': 'fastapi',
            'check': 'fastapi',
            'fallback': None
        },
        {
            'name': 'uvicorn',
            'check': 'uvicorn',
            'fallback': None
        },
        {
            'name': 'streamlit',
            'check': 'streamlit',
            'fallback': None
        },
        {
            'name': 'pydantic',
            'check': 'pydantic',
            'fallback': None
        }
    ]
    
    installed_count = 0
    total_count = len(packages)
    
    for package in packages:
        if install_with_fallback(package['name'], package.get('fallback')):
            installed_count += 1
    
    print(f"\n📊 Installation Summary:")
    print(f"✅ Installed: {installed_count}/{total_count} packages")
    
    if installed_count >= 2:  # At least FastAPI and Uvicorn
        print("🎉 Minimum requirements met! You can start the platform.")
        print("\nTo start the platform:")
        print("  python start_simple_platform.py")
    else:
        print("❌ Insufficient packages installed.")
        print("\n💡 Manual installation options:")
        print("1. Download packages manually from https://pypi.org/")
        print("2. Use a different network connection")
        print("3. Install from local wheel files if available")
        
        # Create a simple requirements file for manual installation
        with open('requirements_manual.txt', 'w') as f:
            f.write("# Manual installation requirements\n")
            f.write("# Download these packages from https://pypi.org/\n\n")
            for package in packages:
                f.write(f"{package['name']}\n")
        
        print("4. Created requirements_manual.txt for reference")

if __name__ == "__main__":
    main()
